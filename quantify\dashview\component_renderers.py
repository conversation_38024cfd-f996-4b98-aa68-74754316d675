"""
组件渲染器模块

该模块包含所有UI组件的渲染函数，从app.py中分离出来以提高代码组织性和可维护性。

主要功能：
1. 股票数据卡片渲染
2. 分析结果渲染
3. 图表组件渲染
4. 报告组件渲染
5. 任务队列组件渲染

作者: AI Assistant
创建时间: 2025-01-08
"""

import dash_bootstrap_components as dbc
from dash import html, dcc
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
import logging
# 移除不再需要的导入，使用任务优化器替代

logger = logging.getLogger(__name__)

def create_card(title, data_type, stock_list):
    """
    创建一个数据类型的卡片，默认显示前5个可点击的股票
    """
    # 为波动率分析卡片创建特殊的头部按钮
    if data_type == 'volatility_analysis':
        header_buttons = html.Div([
            dbc.Button(
                "刷新数据",
                id="refresh-volatility-global-btn",
                color="warning",
                size="sm",
                className="me-2",
                title="刷新所有股票的波动率数据"
            ),
            html.Button("显示更多", id=f"expand-{data_type}", className="btn btn-sm btn-outline-light")
        ])
    else:
        header_buttons = html.Button("显示更多", id=f"expand-{data_type}", className="btn btn-sm btn-outline-light")
    
    # 创建卡片
    card = dbc.Card([
        dbc.CardHeader([
            html.H4(title, className="card-title"),
            header_buttons
        ], className="d-flex justify-content-between align-items-center"),
        dbc.CardBody([
            # 默认显示列表（前5个）
            html.Div(
                id=f"{data_type}-preview-list",
                className="stock-preview-list",
                style={
                    "maxHeight": "300px",  # 设置最大高度
                    "overflowY": "auto",   # 启用垂直滚动
                    "overflowX": "hidden"  # 隐藏水平滚动条
                }
            ),
            # 完整列表（初始隐藏）
            html.Div(
                id=f"{data_type}-full-list",
                className="full-stock-list",
                style={
                    "display": "none",
                    "maxHeight": "500px",  # 完整列表更大的最大高度
                    "overflowY": "auto",   # 启用垂直滚动
                    "overflowX": "hidden"  # 隐藏水平滚动条
                }
            )
        ], style={"padding": "10px"})  # 减小内边距使内容区域更大
    ], className="mb-4")
    
    return card

def render_basic_info(type_data, stock_data):
    """渲染基本信息"""
    try:
        if not type_data or not isinstance(type_data, dict):
            return [html.P("暂无基本信息数据", className="text-muted")]
        
        content = []
        
        # 基本信息表格
        basic_info_rows = []
        for key, value in type_data.items():
            if key not in ['analysis_date', 'update_time']:
                basic_info_rows.append(
                    html.Tr([
                        html.Td(key, className="fw-bold"),
                        html.Td(str(value) if value is not None else "N/A")
                    ])
                )
        
        if basic_info_rows:
            content.append(
                dbc.Table([
                    html.Tbody(basic_info_rows)
                ], bordered=True, hover=True, responsive=True, className="mb-3")
            )
        
        # 添加更新时间
        if 'analysis_date' in type_data:
            content.append(
                html.Small(f"数据更新时间: {type_data['analysis_date']}", className="text-muted")
            )
        
        return content
        
    except Exception as e:
        logger.error(f"渲染基本信息时出错: {str(e)}")
        return [html.P("渲染基本信息时出错", className="text-danger")]

def render_technical_indicators(type_data, stock_data):
    """渲染技术指标"""
    try:
        if not type_data or not isinstance(type_data, dict):
            return [html.P("暂无技术指标数据", className="text-muted")]
        
        content = []
        
        # 技术指标表格
        indicator_rows = []
        for key, value in type_data.items():
            if key not in ['analysis_date', 'update_time']:
                # 格式化数值
                if isinstance(value, (int, float)):
                    formatted_value = f"{value:.4f}" if abs(value) < 1 else f"{value:.2f}"
                else:
                    formatted_value = str(value) if value is not None else "N/A"
                
                indicator_rows.append(
                    html.Tr([
                        html.Td(key, className="fw-bold"),
                        html.Td(formatted_value)
                    ])
                )
        
        if indicator_rows:
            content.append(
                dbc.Table([
                    html.Tbody(indicator_rows)
                ], bordered=True, hover=True, responsive=True, className="mb-3")
            )
        
        # 添加更新时间
        if 'analysis_date' in type_data:
            content.append(
                html.Small(f"指标计算时间: {type_data['analysis_date']}", className="text-muted")
            )
        
        return content
        
    except Exception as e:
        logger.error(f"渲染技术指标时出错: {str(e)}")
        return [html.P("渲染技术指标时出错", className="text-danger")]

def render_relative_analysis(type_data, stock_data):
    """渲染相对分析"""
    try:
        if not type_data or not isinstance(type_data, dict):
            return [html.P("暂无相对分析数据", className="text-muted")]
        
        content = []
        
        # 相对分析表格
        analysis_rows = []
        for key, value in type_data.items():
            if key not in ['analysis_date', 'update_time']:
                # 格式化数值
                if isinstance(value, (int, float)):
                    formatted_value = f"{value:.4f}" if abs(value) < 1 else f"{value:.2f}"
                else:
                    formatted_value = str(value) if value is not None else "N/A"
                
                analysis_rows.append(
                    html.Tr([
                        html.Td(key, className="fw-bold"),
                        html.Td(formatted_value)
                    ])
                )
        
        if analysis_rows:
            content.append(
                dbc.Table([
                    html.Tbody(analysis_rows)
                ], bordered=True, hover=True, responsive=True, className="mb-3")
            )
        
        # 添加更新时间
        if 'analysis_date' in type_data:
            content.append(
                html.Small(f"分析时间: {type_data['analysis_date']}", className="text-muted")
            )
        
        return content
        
    except Exception as e:
        logger.error(f"渲染相对分析时出错: {str(e)}")
        return [html.P("渲染相对分析时出错", className="text-danger")]

def render_dynamic_analysis(type_data, stock_data):
    """渲染动态分析"""
    try:
        if not type_data or not isinstance(type_data, dict):
            return [html.P("暂无动态分析数据", className="text-muted")]
        
        content = []
        
        # 动态分析表格
        dynamic_rows = []
        for key, value in type_data.items():
            if key not in ['analysis_date', 'update_time']:
                # 格式化数值
                if isinstance(value, (int, float)):
                    formatted_value = f"{value:.4f}" if abs(value) < 1 else f"{value:.2f}"
                else:
                    formatted_value = str(value) if value is not None else "N/A"
                
                dynamic_rows.append(
                    html.Tr([
                        html.Td(key, className="fw-bold"),
                        html.Td(formatted_value)
                    ])
                )
        
        if dynamic_rows:
            content.append(
                dbc.Table([
                    html.Tbody(dynamic_rows)
                ], bordered=True, hover=True, responsive=True, className="mb-3")
            )
        
        # 添加更新时间
        if 'analysis_date' in type_data:
            content.append(
                html.Small(f"动态分析时间: {type_data['analysis_date']}", className="text-muted")
            )
        
        return content
        
    except Exception as e:
        logger.error(f"渲染动态分析时出错: {str(e)}")
        return [html.P("渲染动态分析时出错", className="text-danger")]

def render_macro_analysis(type_data, stock_data):
    """渲染宏观分析"""
    try:
        if not type_data or not isinstance(type_data, dict):
            return [html.P("暂无宏观分析数据", className="text-muted")]
        
        content = []
        
        # 宏观分析表格
        macro_rows = []
        for key, value in type_data.items():
            if key not in ['analysis_date', 'update_time']:
                # 格式化数值
                if isinstance(value, (int, float)):
                    formatted_value = f"{value:.4f}" if abs(value) < 1 else f"{value:.2f}"
                else:
                    formatted_value = str(value) if value is not None else "N/A"
                
                macro_rows.append(
                    html.Tr([
                        html.Td(key, className="fw-bold"),
                        html.Td(formatted_value)
                    ])
                )
        
        if macro_rows:
            content.append(
                dbc.Table([
                    html.Tbody(macro_rows)
                ], bordered=True, hover=True, responsive=True, className="mb-3")
            )
        
        # 添加更新时间
        if 'analysis_date' in type_data:
            content.append(
                html.Small(f"宏观分析时间: {type_data['analysis_date']}", className="text-muted")
            )
        
        return content
        
    except Exception as e:
        logger.error(f"渲染宏观分析时出错: {str(e)}")
        return [html.P("渲染宏观分析时出错", className="text-danger")]

def render_volatility_analysis(type_data, stock_data):
    """渲染波动率分析"""
    try:
        if not type_data or not isinstance(type_data, dict):
            return [html.P("暂无波动率分析数据", className="text-muted")]

        content = []

        # 波动率分析表格
        volatility_rows = []
        for key, value in type_data.items():
            if key not in ['analysis_date', 'update_time']:
                # 格式化数值
                if isinstance(value, (int, float)):
                    formatted_value = f"{value:.4f}" if abs(value) < 1 else f"{value:.2f}"
                else:
                    formatted_value = str(value) if value is not None else "N/A"

                volatility_rows.append(
                    html.Tr([
                        html.Td(key, className="fw-bold"),
                        html.Td(formatted_value)
                    ])
                )

        if volatility_rows:
            content.append(
                dbc.Table([
                    html.Tbody(volatility_rows)
                ], bordered=True, hover=True, responsive=True, className="mb-3")
            )

        # 添加更新时间
        if 'analysis_date' in type_data:
            content.append(
                html.Small(f"波动率分析时间: {type_data['analysis_date']}", className="text-muted")
            )

        return content

    except Exception as e:
        logger.error(f"渲染波动率分析时出错: {str(e)}")
        return [html.P("渲染波动率分析时出错", className="text-danger")]

def render_saved_plots(stock_data, plot_type=None):
    """渲染已保存的图表"""
    try:
        if not stock_data or not isinstance(stock_data, dict) or 'stock_symbol' not in stock_data:
            return []

        # 导入db_ops
        from . import db_operations
        db_ops = db_operations.DBOperations()

        # 使用任务优化器加载图表数据以提高响应速度
        from .task_optimizer import get_task_optimizer

        def load_plots_task():
            return db_ops.get_saved_plots(stock_data['stock_symbol'], plot_type)

        optimizer = get_task_optimizer()
        plots = optimizer.optimize_sync("database_operations", load_plots_task)

        if not plots or not isinstance(plots, dict):
            return []

        plot_displays = []
        for plot_key, plot_data in plots.items():
            if not isinstance(plot_data, dict) or not plot_data.get('data'):
                continue

            # 提取频率信息
            freq = ''
            try:
                freq = plot_key.split('_')[2]
            except (IndexError, ValueError):
                freq = 'unknown'

            plot_type_key = 'vol' if 'volatility' in plot_key else 'ret'
            metadata = plot_data.get('metadata', {})
            if not isinstance(metadata, dict):
                metadata = {}

            # 创建图表显示
            plot_displays.append(
                dbc.Card([
                    dbc.CardHeader([
                        html.H6(f"{metadata.get('title', plot_key)} ({freq})", className="mb-0")
                    ]),
                    dbc.CardBody([
                        dcc.Graph(
                            figure=plot_data['data'],
                            config={'displayModeBar': True, 'displaylogo': False}
                        )
                    ])
                ], className="mb-3")
            )

        return plot_displays

    except Exception as e:
        logger.error(f"渲染图表时出错: {str(e)}")
        return [html.P("渲染图表时出错", className="text-danger")]

def create_plot_cards(stock_data):
    """创建图表卡片"""
    try:
        if not stock_data or 'stock_symbol' not in stock_data:
            return html.Div()

        stock_symbol = stock_data['stock_symbol']
        cards = []

        # 波动率图表卡片
        vol_card = dbc.Card([
            dbc.CardHeader([
                html.H5([
                    html.I(className="fas fa-chart-line me-2"),
                    "波动率分析图表"
                ], className="mb-0"),
                dbc.ButtonGroup([
                    dbc.Button(
                        [html.I(className="fas fa-sync-alt me-1"), "生成图表"],
                        id={
                            "type": "generate-plot",
                            "symbol": stock_symbol,
                            "plot_type": "vol"
                        },
                        color="primary",
                        size="sm"
                    )
                ])
            ], className="d-flex justify-content-between align-items-center"),
            dbc.CardBody([
                # 进度条
                html.Div([
                    dbc.Progress(
                        id={
                            "type": "plot-progress",
                            "symbol": stock_symbol,
                            "plot_type": "vol"
                        },
                        value=0,
                        striped=True,
                        animated=True,
                        style={"visibility": "hidden"}
                    ),
                    html.Div(
                        id={
                            "type": "plot-status",
                            "symbol": stock_symbol,
                            "plot_type": "vol"
                        },
                        className="mt-2 text-center"
                    )
                ]),
                # 图表容器
                html.Div(
                    id={
                        "type": "plot-container",
                        "symbol": stock_symbol,
                        "plot_type": "vol"
                    },
                    children=render_saved_plots(stock_data, "vol"),
                    style={
                        "border": "1px solid #444",
                        "borderRadius": "4px",
                        "backgroundColor": "#1a1a1a",
                        "minHeight": "200px",
                        "maxHeight": "400px",
                        "overflowY": "auto",
                        "overflowX": "hidden"
                    }
                )
            ])
        ], className="mb-4")
        cards.append(vol_card)

        # 收益率图表卡片
        ret_card = dbc.Card([
            dbc.CardHeader([
                html.H5([
                    html.I(className="fas fa-chart-bar me-2"),
                    "收益率分析图表"
                ], className="mb-0"),
                dbc.ButtonGroup([
                    dbc.Button(
                        [html.I(className="fas fa-sync-alt me-1"), "生成图表"],
                        id={
                            "type": "generate-plot",
                            "symbol": stock_symbol,
                            "plot_type": "ret"
                        },
                        color="success",
                        size="sm"
                    )
                ])
            ], className="d-flex justify-content-between align-items-center"),
            dbc.CardBody([
                # 进度条
                html.Div([
                    dbc.Progress(
                        id={
                            "type": "plot-progress",
                            "symbol": stock_symbol,
                            "plot_type": "ret"
                        },
                        value=0,
                        striped=True,
                        animated=True,
                        style={"visibility": "hidden"}
                    ),
                    html.Div(
                        id={
                            "type": "plot-status",
                            "symbol": stock_symbol,
                            "plot_type": "ret"
                        },
                        className="mt-2 text-center"
                    )
                ]),
                # 图表容器
                html.Div(
                    id={
                        "type": "plot-container",
                        "symbol": stock_symbol,
                        "plot_type": "ret"
                    },
                    children=render_saved_plots(stock_data, "ret"),
                    style={
                        "border": "1px solid #444",
                        "borderRadius": "4px",
                        "backgroundColor": "#1a1a1a",
                        "minHeight": "200px",
                        "maxHeight": "400px",
                        "overflowY": "auto",
                        "overflowX": "hidden"
                    }
                )
            ])
        ], className="mb-4")
        cards.append(ret_card)

        return dbc.Row([
            dbc.Col(
                card,
                md=6,
                style={
                    "marginBottom": "15px"  # 添加卡片间距
                }
            ) for card in cards
        ])

    except Exception as e:
        logger.error(f"创建图表卡片时出错: {str(e)}")
        return html.Div([html.P("创建图表卡片时出错", className="text-danger")])

def create_task_item(task, is_current=True):
    """创建任务项组件"""
    try:
        if not task or not isinstance(task, dict):
            return html.Div()

        # 任务状态图标和颜色
        status_config = {
            'pending': {'icon': 'fas fa-clock', 'color': 'warning', 'text': '等待中'},
            'running': {'icon': 'fas fa-spinner fa-spin', 'color': 'primary', 'text': '运行中'},
            'completed': {'icon': 'fas fa-check-circle', 'color': 'success', 'text': '已完成'},
            'failed': {'icon': 'fas fa-times-circle', 'color': 'danger', 'text': '失败'},
            'cancelled': {'icon': 'fas fa-ban', 'color': 'secondary', 'text': '已取消'}
        }

        status = task.get('status', 'pending')
        config = status_config.get(status, status_config['pending'])

        # 进度条
        progress = task.get('progress', 0)
        progress_bar = None
        if is_current and status == 'running':
            progress_bar = dbc.Progress(
                value=progress,
                striped=True,
                animated=True,
                color=config['color'],
                className="mb-2"
            )

        # 任务信息
        task_info = [
            html.Div([
                html.I(className=f"{config['icon']} me-2 text-{config['color']}"),
                html.Strong(task.get('task_name', '未知任务')),
                html.Span(f" - {config['text']}", className=f"text-{config['color']} ms-2")
            ], className="d-flex align-items-center mb-1"),
        ]

        if progress_bar:
            task_info.append(progress_bar)

        # 任务详情
        details = []
        if task.get('worker_pid'):
            details.append(f"进程ID: {task['worker_pid']}")
        if task.get('estimated_duration'):
            details.append(f"预估时长: {task['estimated_duration']}秒")
        if task.get('created_time'):
            details.append(f"创建时间: {task['created_time']}")

        if details:
            task_info.append(
                html.Small(" | ".join(details), className="text-muted")
            )

        # 错误信息
        if status == 'failed' and task.get('error'):
            task_info.append(
                html.Div([
                    html.I(className="fas fa-exclamation-triangle me-1"),
                    html.Small(str(task['error']), className="text-danger")
                ], className="mt-1")
            )

        return dbc.Card([
            dbc.CardBody(task_info, className="py-2")
        ], className="mb-2", outline=True, color=config['color'])

    except Exception as e:
        logger.error(f"创建任务项时出错: {str(e)}")
        return html.Div([html.P("创建任务项时出错", className="text-danger")])

def create_task_queue_components():
    """创建任务队列相关组件"""
    try:
        return [
            # 任务队列模态框（移除重复的按钮）
            dbc.Modal([
                dbc.ModalHeader([
                    dbc.ModalTitle("数据更新队列"),
                    html.Div([
                        dbc.Button(
                            "刷新状态",
                            id="refresh-task-queue-btn",
                            color="primary",
                            size="sm",
                            className="me-2"
                        ),
                        dbc.Button(
                            "清空历史",
                            id="clear-task-history-btn",
                            color="warning",
                            size="sm"
                        )
                    ])
                ]),
                dbc.ModalBody([
                    html.Div(
                        id="task-queue-content",
                        style={
                            "maxHeight": "600px",
                            "overflowY": "auto",
                            "overflowX": "hidden",
                            "padding": "20px"
                        }
                    ),
                    dcc.Interval(
                        id="task-queue-update-interval",
                        interval=2000,  # 每2秒更新一次
                        n_intervals=0,
                        disabled=False  # 默认启用，实时更新任务队列
                    )
                ], style={"padding": "0"}),
                dbc.ModalFooter([
                    dbc.Button("关闭", id="close-task-queue-btn", className="ms-auto")
                ])
            ],
            id="task-queue-modal",
            size="xl",
            is_open=False),

            # 存储任务队列数据
            dcc.Store(id="task-queue-data-store", data={})
        ]

    except Exception as e:
        logger.error(f"创建任务队列组件时出错: {str(e)}")
        return [html.Div([html.P("创建任务队列组件时出错", className="text-danger")])]

def create_beautiful_basis_report(report_data, report_path):
    """创建美观的升贴水分析报告显示"""
    try:
        components = []

        # 报告标题
        components.append(
            dbc.Card([
                dbc.CardHeader([
                    html.H4("中国股指期货升贴水综合分析报告", className="text-center mb-0"),
                    html.Small(f"报告路径: {report_path}", className="text-muted text-center d-block mt-2")
                ])
            ], className="mb-4")
        )

        # 如果有报告数据，创建各个分析部分
        if report_data:
            # 基本统计信息对比
            components.append(create_basic_stats_section(report_data))

            # 相关性分析
            components.append(create_correlation_section(report_data))

            # 升贴水水平分析
            components.append(create_level_analysis_section(report_data))

            # 交易信号分析
            components.append(create_trading_signals_section(report_data))

            # 主要发现
            components.append(create_key_findings_section(report_data))

        return components

    except Exception as e:
        logger.error(f"创建升贴水报告时出错: {str(e)}")
        return [html.Div([html.P("创建报告时出错", className="text-danger")])]

def create_basic_stats_section(report_data):
    """创建基本统计信息部分"""
    try:
        # 导入dash_table
        from dash import dash_table

        # 准备表格数据
        table_data = []
        for index_type in ['50', '300', '1000']:
            if index_type in report_data:
                stats = report_data[index_type]['basic_stats']
                table_data.append({
                    '指数': f"{'上证50' if index_type == '50' else '沪深300' if index_type == '300' else '中证1000'}",
                    '数据期间': stats['data_period'],
                    '样本数': stats['sample_size'],
                    '升贴水均值': f"{stats['basis_mean']:.4f}",
                    '升贴水标准差': f"{stats['basis_std']:.4f}",
                    '升贴水范围': f"[{stats['basis_min']:.4f}, {stats['basis_max']:.4f}]"
                })

        table_columns = [
            {"name": "指数", "id": "指数"},
            {"name": "数据期间", "id": "数据期间"},
            {"name": "样本数", "id": "样本数"},
            {"name": "升贴水均值", "id": "升贴水均值"},
            {"name": "升贴水标准差", "id": "升贴水标准差"},
            {"name": "升贴水范围", "id": "升贴水范围"}
        ]

        return dbc.Card([
            dbc.CardHeader([
                html.H5("1. 基本统计信息", className="mb-0")
            ]),
            dbc.CardBody([
                dash_table.DataTable(
                    data=table_data,
                    columns=table_columns,
                    style_table={'overflowX': 'auto'},
                    style_cell={
                        'textAlign': 'center',
                        'padding': '10px',
                        'backgroundColor': '#2b3e50',
                        'color': 'white',
                        'border': '1px solid #495057'
                    },
                    style_header={
                        'backgroundColor': '#1e2a3a',
                        'fontWeight': 'bold',
                        'color': 'white'
                    },
                    style_data_conditional=[
                        {
                            'if': {'row_index': 'odd'},
                            'backgroundColor': '#34495e'
                        }
                    ]
                )
            ])
        ], className="mb-4")

    except Exception as e:
        logger.error(f"创建基本统计信息部分时出错: {str(e)}")
        return html.Div()

def create_correlation_section(report_data):
    """创建相关性分析部分"""
    try:
        # 导入dash_table
        from dash import dash_table

        # 创建主标题卡片
        title_card = dbc.Card([
            dbc.CardHeader([
                html.H5("2. 相关性分析", className="mb-0")
            ]),
            dbc.CardBody([
                html.P("升贴水与未来收益率的相关性分析结果：", className="mb-3")
            ])
        ], className="mb-3")

        # 创建所有相关性表格
        correlation_cards = []

        # 为每个指数创建相关性表格
        for index_type in ['50', '300', '1000']:
            if index_type in report_data and 'correlation_analysis' in report_data[index_type]:
                index_name = {'50': '上证50', '300': '沪深300', '1000': '中证1000'}[index_type]
                corr_data = report_data[index_type]['correlation_analysis']

                # 准备表格数据
                table_data = []
                for period, result in corr_data.items():
                    table_data.append({
                        '周期': f"{period}日",
                        '皮尔逊相关': f"{result['pearson_corr']:.4f}",
                        'P值': f"{result['pearson_p']:.4f}",
                        '斯皮尔曼相关': f"{result['spearman_corr']:.4f}",
                        'P值_2': f"{result['spearman_p']:.4f}",
                        '样本数': result['sample_size']
                    })

                table_columns = [
                    {"name": "周期", "id": "周期"},
                    {"name": "皮尔逊相关", "id": "皮尔逊相关"},
                    {"name": "P值", "id": "P值"},
                    {"name": "斯皮尔曼相关", "id": "斯皮尔曼相关"},
                    {"name": "P值", "id": "P值_2"},
                    {"name": "样本数", "id": "样本数"}
                ]

                correlation_cards.append(
                    dbc.Card([
                        dbc.CardHeader([
                            html.H6(f"{index_name}指数升贴水与未来收益率相关性", className="mb-0")
                        ]),
                        dbc.CardBody([
                            dash_table.DataTable(
                                data=table_data,
                                columns=table_columns,
                                style_table={'overflowX': 'auto'},
                                style_cell={
                                    'textAlign': 'center',
                                    'padding': '8px',
                                    'backgroundColor': '#2b3e50',
                                    'color': 'white',
                                    'border': '1px solid #495057'
                                },
                                style_header={
                                    'backgroundColor': '#1e2a3a',
                                    'fontWeight': 'bold',
                                    'color': 'white'
                                },
                                style_data_conditional=[
                                    {
                                        'if': {'row_index': 'odd'},
                                        'backgroundColor': '#34495e'
                                    },
                                    {
                                        'if': {
                                            'filter_query': '{皮尔逊相关} > 0.1 || {皮尔逊相关} < -0.1',
                                            'column_id': '皮尔逊相关'
                                        },
                                        'backgroundColor': '#28a745',
                                        'color': 'white'
                                    }
                                ]
                            )
                        ])
                    ], className="mb-3")
                )

        # 返回包含所有组件的Div
        return html.Div([title_card] + correlation_cards)

    except Exception as e:
        logger.error(f"创建相关性分析部分时出错: {str(e)}")
        return html.Div()

def create_level_analysis_section(report_data):
    """创建升贴水水平分析部分"""
    try:
        # 导入dash_table
        from dash import dash_table

        # 创建主标题卡片
        title_card = dbc.Card([
            dbc.CardHeader([
                html.H5("3. 升贴水水平分析", className="mb-0")
            ]),
            dbc.CardBody([
                html.P("不同升贴水水平下的收益率分析：", className="mb-3")
            ])
        ], className="mb-3")

        # 创建所有水平分析表格
        level_cards = []

        # 为每个指数创建水平分析表格
        for index_type in ['50', '300', '1000']:
            if index_type in report_data and 'level_analysis' in report_data[index_type]:
                index_name = {'50': '上证50', '300': '沪深300', '1000': '中证1000'}[index_type]
                level_data = report_data[index_type]['level_analysis']

                # 准备表格数据
                table_data = []
                for level, result in level_data.items():
                    table_data.append({
                        '水平': level,
                        '样本数': result['count'],
                        '5日均收益': f"{result['mean_future_return_5d']:.4f}%",
                        '20日均收益': f"{result['mean_future_return_20d']:.4f}%",
                        '30日均收益': f"{result['mean_future_return_30d']:.4f}%",
                        '40日均收益': f"{result['mean_future_return_40d']:.4f}%"
                    })

                table_columns = [
                    {"name": "水平", "id": "水平"},
                    {"name": "样本数", "id": "样本数"},
                    {"name": "5日均收益", "id": "5日均收益"},
                    {"name": "20日均收益", "id": "20日均收益"},
                    {"name": "30日均收益", "id": "30日均收益"},
                    {"name": "40日均收益", "id": "40日均收益"}
                ]

                level_cards.append(
                    dbc.Card([
                        dbc.CardHeader([
                            html.H6(f"{index_name}指数不同升贴水水平收益率分析", className="mb-0")
                        ]),
                        dbc.CardBody([
                            dash_table.DataTable(
                                data=table_data,
                                columns=table_columns,
                                style_table={'overflowX': 'auto'},
                                style_cell={
                                    'textAlign': 'center',
                                    'padding': '8px',
                                    'backgroundColor': '#2b3e50',
                                    'color': 'white',
                                    'border': '1px solid #495057'
                                },
                                style_header={
                                    'backgroundColor': '#1e2a3a',
                                    'fontWeight': 'bold',
                                    'color': 'white'
                                },
                                style_data_conditional=[
                                    {
                                        'if': {'row_index': 'odd'},
                                        'backgroundColor': '#34495e'
                                    }
                                ]
                            )
                        ])
                    ], className="mb-3")
                )

        # 返回包含所有组件的Div
        return html.Div([title_card] + level_cards)

    except Exception as e:
        logger.error(f"创建升贴水水平分析部分时出错: {str(e)}")
        return html.Div()

def create_trading_signals_section(report_data):
    """创建交易信号分析部分"""
    try:
        return dbc.Card([
            dbc.CardHeader([
                html.H5([
                    html.I(className="fas fa-signal me-2"),
                    "交易信号分析"
                ], className="mb-0")
            ]),
            dbc.CardBody([
                html.P("交易信号分析将在这里显示", className="text-muted")
            ])
        ], className="mb-4")

    except Exception as e:
        logger.error(f"创建交易信号分析部分时出错: {str(e)}")
        return html.Div()

def create_key_findings_section(report_data):
    """创建主要发现部分"""
    try:
        return dbc.Card([
            dbc.CardHeader([
                html.H5([
                    html.I(className="fas fa-lightbulb me-2"),
                    "主要发现"
                ], className="mb-0")
            ]),
            dbc.CardBody([
                html.P("主要发现和建议将在这里显示", className="text-muted")
            ])
        ], className="mb-4")

    except Exception as e:
        logger.error(f"创建主要发现部分时出错: {str(e)}")
        return html.Div()

# ==================== 任务监控界面组件 ====================

def create_task_monitor_card():
    """创建任务监控卡片组件"""
    try:
        return dbc.Card([
            dbc.CardHeader([
                dbc.Row([
                    dbc.Col([
                        html.H5("多进程任务监控", className="mb-0")
                    ], width=8),
                    dbc.Col([
                        dbc.ButtonGroup([
                            dbc.Button(
                                "刷新状态",
                                id="refresh-task-monitor",
                                size="sm",
                                color="outline-primary"
                            ),
                            dbc.Button(
                                "清空历史",
                                id="clear-task-history",
                                size="sm",
                                color="outline-warning"
                            )
                        ], className="float-right")
                    ], width=4)
                ])
            ]),
            dbc.CardBody([
                # 系统统计信息
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H6("CPU使用率", className="card-title"),
                                html.H4(id="cpu-usage-display", className="text-primary"),
                                html.P("系统CPU使用率", className="card-text small")
                            ])
                        ])
                    ], width=3),
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H6("内存使用率", className="card-title"),
                                html.H4(id="memory-usage-display", className="text-info"),
                                html.P("系统内存使用率", className="card-text small")
                            ])
                        ])
                    ], width=3),
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H6("活跃任务", className="card-title"),
                                html.H4(id="active-tasks-display", className="text-warning"),
                                html.P("正在执行的任务数", className="card-text small")
                            ])
                        ])
                    ], width=3),
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H6("已完成任务", className="card-title"),
                                html.H4(id="completed-tasks-display", className="text-success"),
                                html.P("已完成的任务数", className="card-text small")
                            ])
                        ])
                    ], width=3)
                ], className="mb-4"),

                # 任务列表
                html.Div([
                    html.H6("当前任务", className="mb-3"),
                    html.Div(id="current-tasks-list")
                ]),

                # 自动刷新定时器
                dcc.Interval(
                    id="task-monitor-interval",
                    interval=2000,  # 每2秒刷新一次
                    n_intervals=0
                )
            ])
        ], className="mb-4")

    except ImportError:
        # 如果dash组件未导入，返回简单的HTML
        return html.Div("任务监控组件加载失败")

def create_task_status_badge(task_info):
    """创建任务状态徽章"""
    try:
        status_colors = {
            "pending": "secondary",
            "running": "primary",
            "completed": "success",
            "failed": "danger",
            "cancelled": "warning"
        }

        status_icons = {
            "pending": "fas fa-clock",
            "running": "fas fa-spinner fa-spin",
            "completed": "fas fa-check",
            "failed": "fas fa-times",
            "cancelled": "fas fa-ban"
        }

        color = status_colors.get(task_info.status.value, "secondary")
        icon = status_icons.get(task_info.status.value, "fas fa-question")

        return dbc.Badge([
            html.I(className=icon, style={"marginRight": "5px"}),
            task_info.status.value.upper()
        ], color=color, className="me-2")

    except ImportError:
        return html.Span(f"[{task_info.status.value.upper()}]")

def create_task_progress_bar(task_info):
    """创建任务进度条"""
    try:
        if task_info.status.value == "completed":
            color = "success"
        elif task_info.status.value == "failed":
            color = "danger"
        elif task_info.status.value == "running":
            color = "primary"
        else:
            color = "secondary"

        return dbc.Progress(
            value=task_info.progress,
            color=color,
            striped=task_info.status.value == "running",
            animated=task_info.status.value == "running",
            style={"height": "20px"},
            className="mb-2"
        )

    except ImportError:
        return html.Div(f"进度: {task_info.progress:.1f}%")
