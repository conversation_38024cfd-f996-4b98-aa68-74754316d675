import dash
from dash import html, dcc, callback_context, no_update
import dash_bootstrap_components as dbc
from dash.dependencies import Input, Output, State, MATCH, ALL
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import dash_draggable
import json
import traceback
import os
import base64
import io
import matplotlib.pyplot as plt
from base64 import b64encode
import logging
from dash.exceptions import PreventUpdate
from dash import dash_table
import time
from threading import Thread
import threading
import pytz
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
# 移除不再需要的导入，使用任务优化器替代
# import queue  # 如果后续需要可以重新添加

from quantify.technical_strategy.alpha_strategy import analyze_boll_signals
from quantify.option_strategy import OPTION_DB_NAME, OPTION_COLLECTION
from quantify.dashview import component_renderers
from quantify.analysisStockdb import AN<PERSON>Y<PERSON>ER_<PERSON>, ANALYZER_COLLECTION
from .db_operations import DBOperations, initialize_auto_scheduler, global_task_queue
from quantify.logger import logger
from quantify.basis_analysis import BasisAnalysis, BasisReportGenerator
from quantify.dashview.volatility_analysis import (
    get_stock_volatility_card_data, 
    get_volatility_alerts,
    analyze_stock_volatility,
    get_volatility_charts,
)
from quantify.dashview.strategy_center_renderer import (
    StrategyCenterRenderer,
    SignalType,
    AlertSeverity
)
from quantify.dashview.task_optimizer import get_task_optimizer, optimize_task, optimize_task_async, auto_optimize
from quantify.dashview.option_analysis_components import (
    create_historical_data_card,
    create_volatility_structure_card,
    create_investment_tips_card,
    register_option_analysis_callbacks
)
from quantify.dashview.machine_analysis_components import (
    create_machine_analysis_layout,
    register_machine_analysis_callbacks
)
from quantify.dashview.line_chart_page import (
    create_line_chart_layout,
    register_line_chart_callbacks
)
from quantify.dashview.component_renderers import (
    create_card,
    render_basic_info,
    render_technical_indicators,
    render_relative_analysis,
    render_dynamic_analysis,
    render_macro_analysis,
    render_volatility_analysis,
    create_plot_cards,
    create_task_item,
    create_beautiful_basis_report,
    create_trading_signals_section,
    create_key_findings_section
)


# Initialize the Dash app with a modern theme
app = dash.Dash(
    __name__, 
    external_stylesheets=[dbc.themes.CYBORG],
    suppress_callback_exceptions=True  # 添加这个设置
)

# 设置页面标题
app.title = "股票分析Dashboard"

# 初始化数据库操作
db_ops = DBOperations()
# 使用全局任务队列以便在界面中显示所有任务
db_ops.task_queue = global_task_queue

# 初始化策略中心渲染器
strategy_renderer = StrategyCenterRenderer()

# 设置默认弹框最大高度
modal_max_height = "90vh"

# 全局变量用于跟踪刷新状态
stock_refresh_status = {}
new_stock_analysis_status = {}
volatility_refresh_status = {}
option_trade_refresh_status = {}
# 全局变量用于跟踪图表生成状态
chart_generation_status = {}
# 全局变量用于跟踪其他资源密集型任务状态
machine_analysis_status = {}
correlation_analysis_status = {}
basis_report_generation_status = {}
option_comprehensive_analysis_status = {}

# 防重复点击的时间戳记录
_last_click_timestamps = {
    'volatility_alerts_refresh': 0,
    'global_volatility_refresh': 0,
    'stock_refresh': 0,
    'option_trade_refresh': 0,
    'chart_generation': 0
}

# 全局缓存用于存储波动率分析数据
volatility_data_cache = {}
volatility_cache_lock = threading.Lock()
volatility_cache_timestamp = 0

# 全局缓存用于存储策略数据
strategy_data_cache = {}
strategy_cache_lock = threading.Lock()
strategy_cache_timestamp = {}

# 全局缓存用于存储股票基础数据
stocks_data_cache = {}
stocks_cache_lock = threading.Lock()
stocks_cache_timestamp = 0

# 使用多线程加载初始数据以提高启动速度
logger.info("开始多线程加载应用初始数据...")

def load_volatility_data_optimized(symbols):
    """
    使用任务优化器加载波动率分析数据

    Args:
        symbols (list): 股票代码列表

    Returns:
        dict: {symbol: volatility_summary} 格式的数据
    """
    global volatility_data_cache, volatility_cache_lock, volatility_cache_timestamp

    # 检查缓存是否有效（5分钟内）
    current_time = time.time()
    with volatility_cache_lock:
        if current_time - volatility_cache_timestamp < 300:  # 5分钟缓存
            logger.info(f"使用缓存的波动率数据，缓存中有 {len(volatility_data_cache)} 条记录")
            return volatility_data_cache

    logger.info(f"开始优化加载 {len(symbols)} 个股票的波动率数据...")

    def batch_load_volatility():
        """批量加载波动率数据的任务函数"""
        def load_single_volatility(symbol):
            """加载单个股票的波动率数据"""
            try:
                volatility_summary = get_stock_volatility_card_data(symbol)
                return symbol, volatility_summary
            except Exception as e:
                logger.error(f"加载 {symbol} 波动率数据失败: {e}")
                return symbol, None

        results = {}

        # 使用任务优化器的线程池并发加载数据
        optimizer = get_task_optimizer()
        futures = []

        # 提交所有任务到优化器
        for symbol in symbols:
            future = optimizer.optimize_task("stock_data_loading", load_single_volatility, symbol)
            futures.append((future, symbol))

        # 收集结果
        completed_count = 0
        for future, symbol in futures:
            symbol_result, volatility_summary = future.result()
            results[symbol_result] = volatility_summary
            completed_count += 1

            if completed_count % 10 == 0:  # 每10个输出一次进度
                logger.info(f"已完成 {completed_count}/{len(symbols)} 个股票的波动率数据加载")

        return results

    # 使用任务优化器执行批量加载任务
    optimizer = get_task_optimizer()
    results = optimizer.optimize_sync("batch_processing", batch_load_volatility)

    # 更新缓存
    with volatility_cache_lock:
        volatility_data_cache = results
        volatility_cache_timestamp = current_time

    logger.info(f"优化加载完成，成功加载 {len([v for v in results.values() if v is not None])} 个股票的波动率数据")
    return results


def clear_volatility_cache():
    """清理波动率数据缓存"""
    global volatility_data_cache, volatility_cache_lock, volatility_cache_timestamp
    with volatility_cache_lock:
        volatility_data_cache.clear()
        volatility_cache_timestamp = 0
    logger.info("波动率数据缓存已清理")


def load_strategy_data_optimized(market_types=None):
    """
    使用任务优化器加载策略数据

    Args:
        market_types (list): 市场类型列表，如果为None则加载['A', 'H']

    Returns:
        dict: {market_type: strategy_data} 格式的数据
    """
    global strategy_data_cache, strategy_cache_lock, strategy_cache_timestamp

    if market_types is None:
        market_types = ['A', 'H']

    # 检查缓存是否有效（10分钟内）
    current_time = time.time()
    with strategy_cache_lock:
        # 检查所有请求的市场类型是否都有有效缓存
        all_cached = True
        for market_type in market_types:
            cache_time = strategy_cache_timestamp.get(market_type, 0)
            if current_time - cache_time >= 600:  # 10分钟缓存
                all_cached = False
                break

        if all_cached:
            logger.info(f"使用缓存的策略数据，缓存中有 {len(strategy_data_cache)} 个市场类型")
            return {mt: strategy_data_cache.get(mt) for mt in market_types}

    logger.info(f"开始优化加载 {len(market_types)} 个市场类型的策略数据...")

    def batch_load_strategy():
        """批量加载策略数据的任务函数"""
        def load_single_strategy(market_type):
            """加载单个市场类型的策略数据"""
            try:
                strategy_data = db_ops.get_stock_strategy_data(market_type)
                return market_type, strategy_data
            except Exception as e:
                logger.error(f"加载 {market_type} 策略数据失败: {e}")
                return market_type, None

        results = {}
        optimizer = get_task_optimizer()

        # 提交所有任务到优化器
        futures = []
        for market_type in market_types:
            future = optimizer.optimize_task("database_operations", load_single_strategy, market_type)
            futures.append((future, market_type))

        # 收集结果
        for future, market_type in futures:
            market_result, strategy_data = future.result()
            results[market_result] = strategy_data

        return results

    # 使用任务优化器执行批量加载任务
    optimizer = get_task_optimizer()
    results = optimizer.optimize_sync("batch_processing", batch_load_strategy)

    # 更新缓存
    with strategy_cache_lock:
        for market_type, data in results.items():
            strategy_data_cache[market_type] = data
            strategy_cache_timestamp[market_type] = current_time

    logger.info(f"优化策略数据加载完成，成功加载 {len([v for v in results.values() if v is not None])} 个市场类型")
    return results


def load_stocks_data_optimized():
    """
    使用任务优化器加载股票基础数据

    Returns:
        dict: 股票数据
    """
    global stocks_data_cache, stocks_cache_lock, stocks_cache_timestamp

    # 检查缓存是否有效（5分钟内）
    current_time = time.time()
    with stocks_cache_lock:
        if current_time - stocks_cache_timestamp < 300:  # 5分钟缓存
            logger.info(f"使用缓存的股票数据，缓存中有 {len(stocks_data_cache)} 条记录")
            return stocks_data_cache

    logger.info("开始优化加载股票基础数据...")

    def load_stocks_task():
        """加载股票数据任务"""
        try:
            return db_ops.get_all_stocks_with_xueqiu_links()
        except Exception as e:
            logger.error(f"加载股票数据失败: {e}")
            return {}

    # 使用任务优化器加载数据
    optimizer = get_task_optimizer()
    results = optimizer.optimize_sync("stock_data_loading", load_stocks_task)

    # 更新缓存
    with stocks_cache_lock:
        stocks_data_cache = results
        stocks_cache_timestamp = current_time

    logger.info(f"优化股票数据加载完成，成功加载 {len(results)} 条记录")
    return results


def clear_all_caches():
    """清理所有缓存"""
    clear_volatility_cache()

    global strategy_data_cache, strategy_cache_lock, strategy_cache_timestamp
    with strategy_cache_lock:
        strategy_data_cache.clear()
        strategy_cache_timestamp.clear()

    global stocks_data_cache, stocks_cache_lock, stocks_cache_timestamp
    with stocks_cache_lock:
        stocks_data_cache.clear()
        stocks_cache_timestamp = 0

    logger.info("所有数据缓存已清理")
    
def load_initial_data():
    """使用任务优化器加载应用初始数据"""
    optimizer = get_task_optimizer()

    # 并发加载各种数据
    stocks_future = optimizer.optimize_task("stock_data_loading", load_stocks_data_optimized)
    strategy_time_future = optimizer.optimize_task("database_operations", db_ops.get_strategy_update_time)
    current_strategy_future = optimizer.optimize_task("database_operations", db_ops.get_current_strategy)
    strategy_text_time_future = optimizer.optimize_task("database_operations", db_ops.get_current_strategy_update_time)

    # 获取结果
    stocks_data = stocks_future.result()
    strategy_update_time = strategy_time_future.result()
    current_strategy = current_strategy_future.result()
    strategy_text_update_time = strategy_text_time_future.result()

    return stocks_data, strategy_update_time, current_strategy, strategy_text_update_time

# 加载初始数据
stocks_data, strategy_update_time, current_strategy, strategy_text_update_time = load_initial_data()
logger.info(f"应用初始数据加载完成，股票数量: {len(stocks_data)}")

stock_info = [
    {
        'id': stock_id,
        'symbol': data.get('stock_symbol', ''),
        'name': data.get('stock_name', ''),
        'market_type': data.get('market_type', ''),
        'analysis_date': data.get('analysis_date', '')
    }
    for stock_id, data in stocks_data.items()
]

# 定义数据类型
data_types = {
    'basic_info': '基本信息',
    'technical_indicators': '技术指标',
    'relative_analysis': '相对分析',
    'dynamic_analysis': '动态指标',
    'macro_analysis': '宏观分析',
    'stock_strategy': '股票策略',
    'volatility_analysis': '波动率分析'
}

# create_card函数已移至component_renderers.py

# 修改模态框定义
stock_detail_modal = dbc.Modal([
    dbc.ModalHeader(html.H3("股票详情", id="modal-title")),
    dbc.ModalBody(
        html.Div(
            id="modal-content",
            style={
                "maxHeight": f"calc({modal_max_height} - 210px)",  # 设置最大高度为视窗高度的80%减去头部和底部的高度
                "overflowY": "auto",                # 启用垂直滚动
                "overflowX": "hidden",              # 隐藏水平滚动条
                "padding": "10px"                   # 添加内边距
            }
        )
    ),
    dbc.ModalFooter(
        dbc.Button("关闭", id="close-modal", className="ml-auto")
    )
], id="stock-detail-modal", size="xl", is_open=False)

# 修改图片放大的模态框定义
image_modal = dbc.Modal([
    dbc.ModalHeader(html.H3("图表详情", id="image-modal-title")),
    dbc.ModalBody([
        html.Img(id="enlarged-image", style={
            "width": "100%", 
            "max-height": "75vh",  # 增加最大高度从70vh到75vh
            "object-fit": "contain"  # 保持图片比例不变
        })
    ], style={"padding": "0", "min-height": "85vh"}),  # 增加模态框主体的最小高度
    dbc.ModalFooter(
        dbc.Button("关闭", id="close-image-modal", className="ml-auto")
    )
], id="image-modal", size="xl", is_open=False, className="image-modal", style={"max-width": "90%"})

# 添加新股票分析对话框
new_stock_modal = dbc.Modal(
    [
        dbc.ModalHeader(dbc.ModalTitle("分析新股票")),
        dbc.ModalBody([
            dbc.Form([
                dbc.Row([
                    dbc.Label("股票代码", width=3),
                    dbc.Col(
                        dbc.Input(id="new-stock-symbol", type="text", placeholder="例如：SH601398, 09988"),
                        width=9
                    ),
                ], className="mb-3"),
                dbc.Row([
                    dbc.Label("市场类型", width=3),
                    dbc.Col(
                        dbc.Select(
                            id="new-stock-market-type",
                            options=[
                                {"label": "A股", "value": "a"},
                                {"label": "港股", "value": "h"},
                                {"label": "美股", "value": "us"},
                                {"label": "期货", "value": "f"},
                                {"label": "指数", "value": "i"},
                                {"label": "特殊指数", "value": "sina"},
                            ],
                            value="a"
                        ),
                        width=9
                    ),
                ], className="mb-3"),
                dbc.Row([
                    dbc.Label("对比指数代码", width=3),
                    dbc.Col(
                        dbc.Input(id="new-stock-index", type="text", placeholder="例如：SH000300, HKHSI"),
                        width=9
                    ),
                ], className="mb-3"),
                dbc.Row([
                    dbc.Col([
                        html.Div(id="new-stock-error", className="text-danger")
                    ], width=12)
                ], className="mb-3"),
            ]),
        ]),
        dbc.ModalFooter([
            dbc.Button("取消", id="cancel-add-stock", className="me-2", n_clicks=0),
            dbc.Button("添加", id="submit-add-stock", className="btn-primary", n_clicks=0),
        ]),
    ],
    id="new-stock-modal",
    is_open=False,
)

# 添加删除股票对话框
delete_stock_modal = dbc.Modal(
    [
        dbc.ModalHeader(dbc.ModalTitle("删除股票")),
        dbc.ModalBody([
            html.P("请选择要删除的股票代码：", className="mb-3"),
            dbc.Select(
                id="delete-stock-select",
                placeholder="选择股票代码...",
                style={"marginBottom": "15px"}
            ),
            dbc.Form([
                dbc.Row([
                    dbc.Col([
                        dbc.Checklist(
                            options=[
                                {"label": "同时删除分析数据", "value": "delete_analysis_data"}
                            ],
                            value=["delete_analysis_data"],  # 默认选中
                            id="delete-options",
                            inline=True
                        )
                    ], width=12)
                ], className="mb-3"),
                dbc.Row([
                    dbc.Col([
                        html.Div(id="delete-stock-error", className="text-danger")
                    ], width=12)
                ], className="mb-3"),
            ]),
        ]),
        dbc.ModalFooter([
            dbc.Button("取消", id="cancel-delete-stock", className="me-2", n_clicks=0),
            dbc.Button("删除", id="submit-delete-stock", color="danger", n_clicks=0),
        ]),
    ],
    id="delete-stock-modal",
    is_open=False,
)

# 添加策略编辑模态框
strategy_edit_modal = dbc.Modal(
    [
        dbc.ModalHeader(dbc.ModalTitle("编辑当前策略")),
        dbc.ModalBody([
            html.P("请在下方输入您的交易策略描述，可以包含具体的操作计划、指标要求、风险控制等内容。", 
                   className="text-muted mb-3", style={"fontSize": "0.9rem"}),
            dbc.Textarea(
                id="strategy-text-input", 
                value=current_strategy,
                style={
                    "height": "350px",
                    "fontFamily": "'Courier New', monospace",
                    "backgroundColor": "#212529",
                    "color": "#f8f9fa",
                    "border": "1px solid #444",
                    "padding": "10px",
                    "fontSize": "0.9rem"
                }
            ),
            html.Div(id="strategy-edit-error", className="text-danger mt-2")
        ]),
        dbc.ModalFooter([
            dbc.Button("取消", id="cancel-edit-strategy", className="me-2", n_clicks=0),
            dbc.Button("保存", id="save-strategy", className="btn-success", n_clicks=0),
        ]),
    ],
    id="strategy-edit-modal",
    is_open=False,
    size="lg",
)

# create_task_queue_components 函数已移动到 component_renderers.py

# 创建应用布局
app.layout = dbc.Container([
    # 顶部导航栏
    dbc.Navbar([
        dbc.Container([
            html.A(
                dbc.Row([
                    dbc.Col(
                        html.I(className="fas fa-chart-line fa-2x", style={"color": "#17a2b8"}),
                        width="auto"
                    ),
                    dbc.Col(dbc.NavbarBrand("量化分析Dashboard", className="ml-2"), width="auto")
                ], align="center", className="g-0"),
                href="/",
                style={"textDecoration": "none"}
            ),
            dbc.Row([
                dbc.Col([
                    dbc.Button("股票分析", id="stock-analysis-btn", color="primary", className="mr-2"),
                    dbc.Button("期权分析", id="option-analysis-btn", color="success", className="mr-2"),
                    dbc.Button("策略中心", id="strategy-center-btn", color="danger", className="mr-2"),
                    dbc.Button("机器分析", id="machine-analysis-btn", color="secondary", className="mr-2"),
                    dbc.Button("折线图", id="line-chart-btn", color="info", className="mr-2"),
                    dbc.Button("刷新数据", id="refresh-data", color="info", className="mr-2"),
                    dbc.Button("分析新标的", id="analyze-new", color="warning", className="mr-2"),
                    dbc.Button("删除股票", id="delete-stock", color="danger", className="mr-2"),
                    # 在导航栏中添加任务队列按钮
                    dbc.Button(
                        [
                            html.I(className="fas fa-tasks me-2"),
                            "数据更新队列"
                        ],
                        id="nav-task-queue-btn",  # 使用不同的ID
                        color="info",
                        size="sm",
                        className="me-2"
                    ),

                ])
            ], className="ml-auto flex-nowrap mt-3 mt-md-0")
        ])
    ], color="dark", dark=True, className="mb-4"),
    
    # 主内容区
    dbc.Container([
        # 状态信息 - 移除重复的按钮
        dbc.Row([
            dbc.Col([
                html.Div([
                    html.H2("分析概览", id="overview-title"),
                    html.P(id="overview-info")
                ], className="mb-4")
            ])
        ]),
        
        # 当前策略卡片
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader(
                        dbc.Row([
                            dbc.Col(html.H4("当前策略", className="mb-0"), width=8),
                            dbc.Col(
                                dbc.Button(
                                    "编辑策略", 
                                    id="edit-strategy-btn",
                                    color="primary", 
                                    size="sm",
                                    style={"width": "120px", "height": "30px", "lineHeight": "18px"}
                                ),
                                width=4,
                                className="d-flex justify-content-end align-items-center"
                            )
                        ]),
                        className="d-flex align-items-center"
                    ),
                    dbc.CardBody([
                        html.Div([
                            html.P(
                                current_strategy, 
                                id="current-strategy-text",
                                style={
                                    "whiteSpace": "pre-wrap",
                                    "font-family": "'Courier New', monospace",
                                    "height": "120px",
                                    "overflowY": "auto",
                                    "backgroundColor": "#1a1a1a",
                                    "color": "#f8f9fa",
                                    "padding": "10px",
                                    "borderRadius": "4px"
                                }
                            ),
                            html.Small([
                                html.I(className="fas fa-clock mr-1", style={"marginRight": "3px"}),
                                "更新时间: ",
                                html.Span(
                                    id="strategy-text-update-time",
                                    children=strategy_text_update_time,
                                    style={"color": "#fd7e14", "fontWeight": "500"}
                                )
                            ], style={"fontSize": "0.75rem", "color": "#6c757d", "marginTop": "8px", "display": "block"})
                        ])
                    ])
                ], className="mb-4")
            ], width=12)
        ], id="current-strategy-card"),
        
        # 数据卡片区
        dbc.Row([
            # 股票分析界面
            html.Div([
                # 股票策略卡片 - 独占一行，占据三倍宽度
                dbc.Row([
                    dbc.Col([
                        create_card(data_types['stock_strategy'], 'stock_strategy', stock_info),
                    ], md=12)
                ], className="mb-3"),
                # 其他卡片重新布局
                dbc.Row([
                    dbc.Col([
                        create_card(data_types['basic_info'], 'basic_info', stock_info),
                        create_card(data_types['relative_analysis'], 'relative_analysis', stock_info),
                    ], md=4),
                    dbc.Col([
                        create_card(data_types['technical_indicators'], 'technical_indicators', stock_info),
                        create_card(data_types['dynamic_analysis'], 'dynamic_analysis', stock_info),
                    ], md=4),
                    dbc.Col([
                        create_card(data_types['macro_analysis'], 'macro_analysis', stock_info),
                        create_card(data_types['volatility_analysis'], 'volatility_analysis', stock_info),
                    ], md=4)
                ])
            ], id="stock-analysis-content"),
            
            # 期权分析界面
            html.Div([
                # 期权数据概览卡片
                dbc.Card([
                    dbc.CardHeader(html.H5("期权数据概览")),
                    dbc.CardBody([
                        dbc.Row([
                            dbc.Col([
                                html.H6("选择指数类型"),
                                dcc.Dropdown(
                                    id='index-type-dropdown',
                                    options=[
                                        {'label': '上证50', 'value': '50'},
                                        {'label': '沪深300', 'value': '300'},
                                        {'label': '中证1000', 'value': '1000'}
                                    ],
                                    value='300',
                                    className="mb-3"
                                ),
                                html.H6("选择日期"),
                                dcc.DatePickerSingle(
                                    id='date-picker',
                                    date=datetime.now(),
                                    max_date_allowed=datetime.now() + relativedelta(days=2),
                                    className="mb-3"
                                ),
                                dbc.Button("加载数据", id="load-option-data", color="primary", className="mr-2"),
                                dbc.Button("刷新期权数据", id="refresh-option-data", color="success")
                            ], width=4),
                            dbc.Col([
                                html.Div(id="option-data-display")
                            ], width=8)
                        ])
                    ])
                ], className="mb-4"),
                
                # 升贴水分析卡片
                dbc.Card([
                    dbc.CardHeader([
                        dbc.Row([
                            dbc.Col([
                                html.H5("期货升贴水分析", className="mb-0")
                            ], width=10),
                            dbc.Col([
                                dbc.Button(
                                    "展开全部",
                                    id="expand-futures-gap",
                                    size="sm",
                                    color="outline-primary",
                                    className="float-right"
                                )
                            ], width=2)
                        ])
                    ]),
                    dbc.CardBody([
                        # 默认显示区域（沪深300）
                        html.Div(id="futures-gap-default-display"),
                        
                        # 展开显示区域（所有指数）
                        html.Div(
                            id="futures-gap-expanded-display",
                            style={"display": "none"}
                        ),
                        
                        # 加载按钮
                        dbc.Row([
                            dbc.Col([
                                dbc.Button(
                                    "加载升贴水数据",
                                    id="load-futures-gap-data",
                                    color="primary",
                                    className="mt-3 me-2"
                                ),
                                dbc.Button(
                                    "刷新缓存",
                                    id="refresh-futures-gap-cache",
                                    color="warning",
                                    className="mt-3 me-2",
                                    title="清除缓存并重新获取最新数据"
                                ),
                                dbc.Button(
                                    "生成分析报告",
                                    id="generate-basis-analysis-report",
                                    color="success",
                                    className="mt-3 me-2",
                                    title="生成详细的升贴水分析报告"
                                ),
                                dbc.Button(
                                    "查看分析报告",
                                    id="view-basis-analysis-report",
                                    color="info",
                                    className="mt-3",
                                    title="查看已生成的分析报告"
                                )
                            ], width=12)
                        ]),
                        
                        # 分析报告显示区域
                        html.Div(
                            id="basis-analysis-report-display",
                            className="mt-4",
                            style={"display": "none"}
                        )
                    ])
                ], className="mb-4"),

                # 期权传统金融模型分析卡片
                html.Div([
                    html.H4("期权传统金融模型分析", className="mb-3"),

                    # 历史数据分析卡片
                    html.Div(id="historical-data-card-container"),

                    # 波动率结构分析卡片
                    html.Div(id="volatility-structure-card-container"),

                    # 投资提示卡片
                    html.Div(id="investment-tips-card-container")
                ], className="mb-4"),

                # 数据存储
                dcc.Store(id="futures-gap-data-store", data={}),
                dcc.Store(id="basis-analysis-report-store", data={})
            ], id="option-analysis-content", style={"display": "none"}),
            
            # 策略中心界面 - 使用新的渲染器
            html.Div([
                html.Div(id="strategy-center-main-content"),
                
                # 数据存储
                dcc.Store(id="strategy-signals-store", data={}),
                dcc.Store(id="volatility-alerts-store", data={}),
                dcc.Store(id="stock-trade-signals-store", data={}),
                dcc.Store(id="option-trade-signals-store", data={}),
            ], id="strategy-center-content", style={"display": "none"}),

            # 机器分析界面
            html.Div([
                html.Div(id="machine-analysis-main-content")
            ], id="machine-analysis-content", style={"display": "none"}),

            # 折线图界面
            html.Div([
                html.Div(id="line-chart-main-content")
            ], id="line-chart-content", style={"display": "none"}),
        ]),
        
        # 股票详情模态框
        stock_detail_modal,
        
        # 添加图片放大的模态框
        image_modal,
        
        # 告警详情模态框
        dbc.Modal(
            [
                dbc.ModalHeader(dbc.ModalTitle(id="alert-detail-modal-title")),
                dbc.ModalBody(
                    html.Div(
                        id="alert-detail-modal-body",
                        style={
                            "maxHeight": "85vh",  # 增大最大高度为视窗高度的85%
                            "minHeight": "500px",  # 设置最小高度，确保能显示所有内容
                            "overflowY": "auto",  # 启用垂直滚动（如果内容超过最大高度）
                            "overflowX": "hidden",  # 隐藏水平滚动条
                            "padding": "15px"  # 增加内边距
                        }
                    )
                ),
                dbc.ModalFooter(
                    dbc.Button("关闭", id="close-alert-detail-modal", className="ml-auto")
                )
            ],
            id="alert-detail-modal",
            size="xl",
            is_open=False,
            style={
                "max-width": "98%",  # 进一步增大模态框最大宽度到98%
                "width": "98%"       # 设置固定宽度为98%
            }
        ),
        
        # 策略信号详情模态框
        dbc.Modal(
            [
                dbc.ModalHeader(dbc.ModalTitle(id="signal-detail-modal-title")),
                dbc.ModalBody(
                    html.Div(
                        id="signal-detail-modal-body",
                        style={
                            "maxHeight": "85vh",
                            "minHeight": "500px",
                            "overflowY": "auto",
                            "overflowX": "hidden",
                            "padding": "15px"
                        }
                    )
                ),
                dbc.ModalFooter(
                    dbc.Button("关闭", id="close-signal-detail-modal", className="ml-auto")
                )
            ],
            id="signal-detail-modal",
            size="xl",
            is_open=False,
            style={
                "max-width": "98%",
                "width": "98%"
            }
        ),
        
        # 隐藏的数据存储
        html.Div(id="hidden-triggers", style={"display": "none"}),
        dcc.Store(id="stocks-data-store", data=stocks_data),
        dcc.Store(id="option-data-store", data={}),
        
        # 添加策略数据存储
        dcc.Store(id="strategy-data-store", data={
            "text": current_strategy,
            "update_time": strategy_text_update_time
        }),
        
        # 添加定时器用于定期更新策略数据
        dcc.Interval(
            id='strategy-refresh-interval',
            interval=30000,  # 每30秒刷新一次
            n_intervals=0
        ),
        
        # 添加定时器组件用于更新进度
        dcc.Interval(
            id='progress-interval',
            interval=2000,
            n_intervals=0,
            disabled=True
        ),
        
        # 添加策略中心专用刷新定时器
        dcc.Interval(
            id='strategy-center-refresh-interval',
            interval=300000,  # 每5分钟检查一次
            n_intervals=0,
            disabled=False
        ),
        
        # 添加策略中心刷新触发器存储
        dcc.Store(id="strategy-center-refresh-trigger", data={"timestamp": 0}),

        # 添加策略中心展开状态存储
        dcc.Store(id="strategy-center-expand-state", data={}),
        
        dcc.Store(id="error-status-store", data={}),
        
        # 添加期权数据完整表格的模态框 - 使用customWidth替代size属性
        dbc.Modal(
            [
                dbc.ModalHeader("期权完整数据", id="full-table-modal-header", close_button=True),
                dbc.ModalBody([
                                dash_table.DataTable(
                id="full-option-table",
                fixed_rows={'headers': True},
                fixed_columns={'headers': True, 'data': 1},
                style_table={
                    'height': 'calc(100vh - 200px)',
                    'width': '100%',
                    'overflowY': 'auto',
                    'overflowX': 'auto',
                    'minWidth': '100%'
                },
                style_header={
                    'backgroundColor': 'rgb(30, 30, 30)',
                    'color': 'white',
                    'fontWeight': 'bold',
                    'height': 'auto',
                    'whiteSpace': 'normal'
                },
                style_data={
                    'backgroundColor': 'rgb(50, 50, 50)',
                    'color': 'white'
                },
                style_cell={
                    'textAlign': 'left',
                    'minWidth': '120px',
                    'width': '150px', 
                    'maxWidth': '200px',
                    'overflow': 'hidden',
                    'textOverflow': 'ellipsis',
                    'padding': '5px',
                    'border': '1px solid #444'
                },
                css=[{
                    'selector': '.dash-spreadsheet td.cell--fixed-left, .dash-spreadsheet th.column-header--fixed-left',
                    'rule': 'position: sticky !important; left: 0 !important; z-index: 999 !important;'
                }]
                    )
                ]),
                dbc.ModalFooter(
                    dbc.Button("关闭", id="close-full-table-modal", className="ml-auto")
                ),
            ],
            id="full-table-modal",
            # 移除size属性，改用className和样式控制
            # size="xl", 
            className="full-width-modal",
            style={
                "maxWidth": "95%", 
                "width": "95%"
            },
            backdrop="static",
        ),
        
        # 在布局中添加进度存储和弹出框组件（放在适当位置，例如app.layout中）
        dcc.Store(id="refresh-progress-store", data={"progress": 0, "message": ""}),
        
        # 添加独立的期权刷新进度存储
        dcc.Store(id="option-refresh-progress-store", data={"progress": 0, "message": ""}),

        # 添加刷新进度弹出框
        dbc.Modal(
            [
                dbc.ModalHeader(dbc.ModalTitle("数据刷新"), style={"paddingBottom": "10px"}),
                dbc.ModalBody([
                    html.Div(id="refresh-progress-message", className="mb-2", style={"textAlign": "center", "fontWeight": "bold"}),
                    dbc.Progress(
                        id="refresh-progress-bar",
                        value=0,
                        striped=True,
                        animated=True,
                        className="mb-2"
                    ),
                ], style={"padding": "10px 15px 15px 15px"}),
            ],
            id="refresh-progress-modal",
            is_open=False,
            centered=True,
            size="sm",
        ),

        # 添加期权刷新进度弹出框
        dbc.Modal(
            [
                dbc.ModalHeader(dbc.ModalTitle("期权数据刷新"), style={"paddingBottom": "10px"}),
                dbc.ModalBody([
                    html.Div(id="option-refresh-progress-message", className="mb-2", style={"textAlign": "center", "fontWeight": "bold"}),
                    dbc.Progress(
                        id="option-refresh-progress-bar",
                        value=0,
                        striped=True,
                        animated=True,
                        className="mb-2"
                    ),
                ], style={"padding": "10px 15px 15px 15px"}),
            ],
            id="option-refresh-progress-modal",
            is_open=False,
            centered=True,
            size="sm",
        ),
        
        # 添加股票数据刷新进度更新间隔组件
        dcc.Interval(
            id='refresh-progress-interval',
            interval=2000,  # 每2秒更新一次
            n_intervals=0,
            disabled=True
        ),
        
        # 添加期权数据刷新进度更新间隔组件
        dcc.Interval(
            id='option-refresh-progress-interval',
            interval=2000,  # 每2秒更新一次
            n_intervals=0,
            disabled=True
        ),
        
        # 添加新股票分析对话框
        new_stock_modal,
        
        # 添加删除股票对话框
        delete_stock_modal,
        
        # 添加策略编辑模态框
        strategy_edit_modal,
        
        # 在布局末尾添加任务队列组件
        *component_renderers.create_task_queue_components(),


    ]),
    
    # 页脚
    html.Footer([
        dbc.Container([
            html.P("notIbutWind股票量化分析系统 2025", className="text-center text-muted")
        ])
    ], className="mt-5 py-3 bg-dark"),
    
    # 添加按钮（这个按钮会在数据加载时动态显示）
    html.Div(id="show-full-table-container"),

    # 添加调试面板用于显示 stocks_data 内容
    dbc.Modal(
        [
            dbc.ModalHeader(dbc.ModalTitle("调试：stocks_data 内容")),
            dbc.ModalBody(
                html.Div(
                    id="debug-stocks-data-content",
                    style={
                        "maxHeight": "70vh",
                        "overflowY": "auto",
                        "fontFamily": "monospace",
                        "fontSize": "12px",
                        "whiteSpace": "pre-wrap"
                    }
                )
            ),
            dbc.ModalFooter(
                dbc.Button("关闭", id="close-debug-modal", className="ml-auto")
            )
        ],
        id="debug-stocks-data-modal",
        size="xl",
        is_open=False
    ),

    # 添加调试按钮到导航栏
    html.Div(
        dbc.Button(
            "调试 stocks_data",
            id="debug-stocks-data-btn",
            color="warning",
            size="sm",
            className="me-2"
        ),
        style={
            "position": "fixed",
            "top": "10px",
            "right": "10px",
            "zIndex": "9999"
        }
    ),
])

# 修改生成股票列表的回调
@app.callback(
    [Output(f"{data_type}-preview-list", "children") for data_type in data_types],
    [Input("stocks-data-store", "data")]
)
def generate_stock_lists(stocks_data):
    if not stocks_data:
        return [html.P("无数据")] * len(data_types)
    
    preview_lists = []
    
    for data_type in data_types:
        if data_type == 'stock_strategy':
            # 策略卡片特殊处理
            # 添加刷新按钮和更新时间显示
            header_row = dbc.Row([
                dbc.Col(
                    market_dropdown := dcc.Dropdown(
                        id='market-type-dropdown',
                        options=[
                            {'label': 'A股', 'value': 'A'},
                            {'label': '港股', 'value': 'H'}
                        ],
                        value='A',
                        className="mb-3"
                    ),
                    width=8
                ),
                dbc.Col(
                    dbc.Button(
                        "刷新策略", 
                        id="refresh-strategy-btn",
                        color="primary", 
                        size="sm",
                        className="mb-3"
                    ),
                    width=4,
                    className="d-flex justify-content-end"
                )
            ])
            
            # 添加更新时间显示
            update_time_row = dbc.Row([
                dbc.Col([
                    html.Div([
                        html.Small([
                            html.I(className="fas fa-clock mr-1", style={"marginRight": "3px"}),
                            "更新时间: ",
                            html.Span(id="strategy-update-time", 
                                    children=strategy_update_time,
                                    style={"color": "#fd7e14", "fontWeight": "500"})
                        ], style={"fontSize": "0.75rem", "color": "#6c757d"})
                    ], className="text-right mb-2")
                ], width=12)
            ])
            
            strategy_table = dash_table.DataTable(
                id='strategy-table',
                style_table={'overflowX': 'auto'},
                style_cell={
                    'backgroundColor': 'rgb(50, 50, 50)',
                    'color': 'white',
                    'textAlign': 'left'
                },
                style_header={
                    'backgroundColor': 'rgb(30, 30, 30)',
                    'fontWeight': 'bold'
                },
                page_size=20
            )
            
            preview_content = html.Div([header_row, update_time_row, strategy_table])
            preview_lists.append(preview_content)
            
        elif data_type == 'volatility_analysis':
            # 波动率分析卡片特殊处理 - 使用多线程加载
            stock_items = []

            # 收集所有股票代码
            symbols = [data.get('stock_symbol', '') for stock_id, data in stocks_data.items()
                      if data.get('stock_symbol', '')]

            # 使用任务优化器加载波动率数据
            volatility_data = load_volatility_data_optimized(symbols)

            for stock_id, data in stocks_data.items():
                stock_symbol = data.get('stock_symbol', '')
                stock_name = data.get('stock_name', '')

                # 从预加载的数据中获取波动率分析数据
                try:
                    volatility_summary = volatility_data.get(stock_symbol)
                    if volatility_summary:
                        # 使用波动率分析的实际更新时间，而不是stocks_data中的时间
                        analysis_date = volatility_summary.get('analysis_date', data.get('analysis_date', ''))
                        recommendation = volatility_summary['overall_recommendation']
                        alert_count = volatility_summary['alert_count']
                        
                        # 设置推荐颜色
                        if recommendation == "做多":
                            rec_color = "#28a745"
                            rec_icon = "fas fa-arrow-up"
                        elif recommendation == "做空":
                            rec_color = "#dc3545"
                            rec_icon = "fas fa-arrow-down"
                        elif "建仓" in recommendation:
                            rec_color = "#ffc107"
                            rec_icon = "fas fa-plus-circle"
                        else:
                            rec_color = "#6c757d"
                            rec_icon = "fas fa-eye"
                        
                        # 设置告警徽章颜色
                        if alert_count >= 3:
                            alert_color = "#dc3545"  # 红色 - 高风险
                        elif alert_count >= 1:
                            alert_color = "#ffc107"  # 黄色 - 中风险
                        else:
                            alert_color = "#28a745"  # 绿色 - 低风险
                        
                        stock_items.append(
                            html.Div([
                                dbc.Card([
                                    dbc.CardBody([
                                        dbc.Row([
                                            dbc.Col([
                                                html.Div([
                                                    html.A(
                                                        f"{stock_name} ({stock_symbol})",
                                                        id={"type": "stock-detail-link", "index": f"{data_type}-{stock_id}"},
                                                        className="stock-link",
                                                        style={"textDecoration": "none", "color": "#fff", "marginRight": "10px"}
                                                    ),
                                                    html.A(
                                                        html.I(className="fas fa-external-link-alt", style={"fontSize": "12px"}),
                                                        href=data.get('xueqiu_url', ''),
                                                        target="_blank",
                                                        title="在雪球查看详情",
                                                        style={"color": "#17a2b8", "textDecoration": "none"}
                                                    ) if data.get('xueqiu_url') else None
                                                ], style={"display": "flex", "alignItems": "center"})
                                            ], width=8),
                                            dbc.Col([
                                                html.Span(
                                                    f"{alert_count}",
                                                    className="badge",
                                                    style={
                                                        "backgroundColor": alert_color,
                                                        "borderRadius": "50%",
                                                        "padding": "0.3em 0.6em",
                                                        "fontSize": "0.75em"
                                                    }
                                                )
                                            ], width=4, className="text-right")
                                        ]),
                                        html.Hr(style={"margin": "0.5rem 0"}),
                                        dbc.Row([
                                            dbc.Col([
                                                html.Div([
                                                    html.I(className=rec_icon, style={"color": rec_color}),
                                                    html.Span(f" {recommendation}", style={"color": rec_color, "marginLeft": "5px"})
                                                ])
                                            ], width=8),
                                            dbc.Col([
                                                html.Small(analysis_date, className="text-muted")
                                            ], width=4, className="text-right")
                                        ])
                                    ])
                                ], 
                                style={
                                    "backgroundColor": "#2c2c2c", 
                                    "border": "1px solid #444",
                                    "marginBottom": "10px"
                                })
                            ], className="stock-item")
                        )
                    else:
                        # 无波动率分析数据时的默认显示
                        stock_items.append(
                            html.Div([
                                dbc.Card([
                                    dbc.CardBody([
                                        html.Div([
                                            html.A(
                                                f"{stock_name} ({stock_symbol})",
                                                id={"type": "stock-detail-link", "index": f"{data_type}-{stock_id}"},
                                                className="stock-link",
                                                style={"textDecoration": "none", "color": "#fff", "marginRight": "10px"}
                                            ),
                                            html.A(
                                                html.I(className="fas fa-external-link-alt", style={"fontSize": "12px"}),
                                                href=data.get('xueqiu_url', ''),
                                                target="_blank",
                                                title="在雪球查看详情",
                                                style={"color": "#17a2b8", "textDecoration": "none"}
                                            ) if data.get('xueqiu_url') else None
                                        ], style={"display": "flex", "alignItems": "center"}),
                                        html.Hr(style={"margin": "0.5rem 0"}),
                                        html.Div([
                                            html.I(className="fas fa-chart-line", style={"color": "#6c757d"}),
                                            html.Span(" 点击进行波动率分析", style={"color": "#6c757d", "marginLeft": "5px"})
                                        ])
                                    ])
                                ], 
                                style={
                                    "backgroundColor": "#2c2c2c", 
                                    "border": "1px solid #444",
                                    "marginBottom": "10px"
                                })
                            ], className="stock-item")
                        )
                except Exception as e:
                    logger.error(f"处理 {stock_symbol} 波动率分析数据失败: {e}")
                    # 错误时的默认显示
                    stock_items.append(
                        html.Div([
                            dbc.Card([
                                dbc.CardBody([
                                    html.A(
                                        f"{stock_name} ({stock_symbol})",
                                        id={"type": "stock-detail-link", "index": f"{data_type}-{stock_id}"},
                                        className="stock-link",
                                        style={"textDecoration": "none", "color": "#fff"}
                                    ),
                                    html.Hr(style={"margin": "0.5rem 0"}),
                                    html.Div([
                                        html.I(className="fas fa-exclamation-triangle", style={"color": "#ffc107"}),
                                        html.Span(" 点击进行波动率分析", style={"color": "#ffc107", "marginLeft": "5px"})
                                    ])
                                ])
                            ],
                            style={
                                "backgroundColor": "#2c2c2c",
                                "border": "1px solid #444",
                                "marginBottom": "10px"
                            })
                        ], className="stock-item")
                    )
            
            if not stock_items:
                preview_lists.append(html.P("暂无波动率分析数据"))
            else:
                preview_lists.append(
                    html.Div(
                        stock_items,
                        className="stock-list-container",
                        style={
                            "maxHeight": "300px",
                            "overflowY": "auto",
                            "overflowX": "hidden",
                            "transition": "max-height 0.3s ease-in-out"
                        }
                    )
                )
                
        else:
            # 其他卡片的常规处理
            stock_items = []
            
            for stock_id, data in stocks_data.items():
                symbol = data.get('stock_symbol', '')
                name = data.get('stock_name', '')

                # 创建股票链接和雪球跳转链接
                link_container = html.Div([
                    html.A(
                        f"{name} ({symbol})",
                        id={"type": "stock-detail-link", "index": f"{data_type}-{stock_id}"},
                        className="stock-link",
                        style={"marginRight": "10px"}
                    ),
                    html.A(
                        html.I(className="fas fa-external-link-alt", style={"fontSize": "12px"}),
                        href=data.get('xueqiu_url', ''),
                        target="_blank",
                        title="在雪球查看详情",
                        style={"color": "#17a2b8", "textDecoration": "none"}
                    ) if data.get('xueqiu_url') else None
                ], style={"display": "flex", "alignItems": "center"})

                item = html.Div([link_container], className="stock-item")
                stock_items.append(item)
            
            if not stock_items:
                preview_lists.append(html.P("暂无数据"))
            else:
                # 创建一个包含所有项目的容器，通过maxHeight和overflow控制显示
                preview_lists.append(
                    html.Div(
                        stock_items,
                        className="stock-list-container",
                        style={
                            "maxHeight": "300px",  # 初始高度限制
                            "overflowY": "auto",   # 启用垂直滚动
                            "overflowX": "hidden", # 隐藏水平滚动条
                            "transition": "max-height 0.3s ease-in-out"  # 添加过渡效果
                        }
                    )
                )
    
    return preview_lists

@app.callback(
    [Output(f"{data_type}-preview-list", "style") for data_type in data_types],
    [Output(f"expand-{data_type}", "children") for data_type in data_types],
    [Input(f"expand-{data_type}", "n_clicks") for data_type in data_types],
    [State(f"{data_type}-preview-list", "style") for data_type in data_types]
)
def toggle_stock_lists(*args):
    # 分割参数
    n_clicks = args[:len(data_types)]
    current_styles = args[len(data_types):]
    
    # 找出触发回调的按钮
    ctx = callback_context
    if not ctx.triggered:
        base_style = {
            "maxHeight": "300px",
            "overflowY": "auto",
            "overflowX": "hidden",
            "transition": "max-height 0.3s ease-in-out"
        }
        return [base_style for _ in data_types] + ["显示更多" for _ in data_types]
    
    triggered_id = ctx.triggered[0]['prop_id'].rsplit('.', 1)[0]
    triggered_type = triggered_id.replace("expand-", "")
    
    # 准备返回值
    new_styles = []
    new_button_texts = []
    
    data_type_list = list(data_types.keys())
    
    for i, dt in enumerate(data_type_list):
        if dt == triggered_type:
            # 切换当前点击的类型
            current_style = current_styles[i] if current_styles and i < len(current_styles) else {"maxHeight": "300px"}
            is_collapsed = current_style.get("maxHeight") == "300px"
            
            new_style = {
                "maxHeight": "none" if is_collapsed else "300px",
                "overflowY": "auto",
                "overflowX": "hidden",
                "transition": "max-height 0.3s ease-in-out"
            }
            new_styles.append(new_style)
            new_button_texts.append("收起" if is_collapsed else "显示更多")
        else:
            # 保持其他类型不变
            current_style = current_styles[i] if current_styles and i < len(current_styles) else {
                "maxHeight": "300px",
                "overflowY": "auto",
                "overflowX": "hidden",
                "transition": "max-height 0.3s ease-in-out"
            }
            new_styles.append(current_style)
            is_collapsed = current_style.get("maxHeight") == "300px"
            new_button_texts.append("显示更多" if is_collapsed else "收起")
    
    return new_styles + new_button_texts

# 修改显示股票详情模态框的回调函数
@app.callback(
    [
        Output("stock-detail-modal", "is_open"),
        Output("modal-title", "children"),
        Output("modal-content", "children"),
        Output("stock-detail-modal", "className")  # 改用className来控制位置
    ],
    [
        Input({"type": "stock-detail-link", "index": dash.dependencies.ALL}, "n_clicks"),
        Input("close-modal", "n_clicks")
    ],
    [
        State("stock-detail-modal", "is_open"),
        State("stocks-data-store", "data")
    ]
)
def toggle_modal(stock_clicks, close_clicks, is_open, stocks_data):
    ctx = callback_context
    
    if not ctx.triggered:
        return False, "", [], "modal"
    
    triggered_id = ctx.triggered[0]['prop_id'].rsplit('.', 1)[0]
    
    if triggered_id == "close-modal":
        return False, "", [], "modal"
    
    if not any(click for click in stock_clicks if click is not None):
        return False, "", [], "modal"
    
    for trigger in ctx.triggered:
        if "stock-detail-link" in trigger['prop_id']:
            triggered_id = json.loads(trigger['prop_id'].rsplit('.', 1)[0])
            index_parts = triggered_id['index'].split('-')
            data_type = index_parts[0]
            stock_id = '-'.join(index_parts[1:])
            
            stock_data = stocks_data.get(stock_id, {})
            if not stock_data:
                return is_open, "数据未找到", html.P("无法找到该股票的数据"), "modal"
            
            title = f"{stock_data.get('stock_symbol', '')} - {stock_data.get('stock_name', '')} ({data_types[data_type]})"
            
            content = []
            type_data = stock_data.get(data_type, {})
            
            if data_type == 'basic_info':
                content = render_basic_info(type_data, stock_data)
            elif data_type == 'technical_indicators':
                content = render_technical_indicators(type_data, stock_data)
            elif data_type == 'relative_analysis':
                content = render_relative_analysis(type_data, stock_data)
            elif data_type == 'dynamic_analysis':
                content = render_dynamic_analysis(type_data, stock_data)
            elif data_type == 'macro_analysis':
                content = render_macro_analysis(type_data, stock_data)
            elif data_type == 'volatility_analysis':
                content = render_volatility_analysis(type_data, stock_data)
            
            return True, title, content, "modal modal-at-click"
    
    return is_open, "", [], "modal"

# 修改图表渲染函数，使用固定容器
def create_gauge_chart(figure, height="250px"):
    """创建带有固定容器的仪表图"""
    return html.Div(
        dcc.Graph(
            figure=figure,
            config={'displayModeBar': False},
            style={'height': '100%', 'width': '100%'}  # 图表适应容器
        ),
        className="chart-container",
        style={'height': height}  # 容器固定高度
    )
    
    
# render_saved_plots 函数已移动到 component_renderers.py

def render_volatility_analysis(data, stock_data):
    """渲染波动率分析部分"""
    if not stock_data or not isinstance(stock_data, dict):
        return html.P("无股票数据")
    
    stock_symbol = stock_data.get('stock_symbol', '')
    if not stock_symbol:
        return html.P("无效的股票代码")
    
    cards = []
    
    # 波动率分析主卡片
    analysis_card = dbc.Card([
        dbc.CardHeader([
            dbc.Row([
                dbc.Col([
                    html.H4([
                        html.I(className="fas fa-chart-area mr-2", style={"color": "#17a2b8"}),
                        "波动率分析"
                    ], className="mb-0")
                ], width=8),
                dbc.Col([
                    dbc.Button(
                        "开始分析",
                        id={
                            "type": "volatility-analysis-btn",
                            "symbol": stock_symbol
                        },
                        color="primary",
                        size="sm"
                    )
                ], width=4, className="d-flex justify-content-end")
            ])
        ]),
        dbc.CardBody([
            # 分析结果显示区
            html.Div(
                id={
                    "type": "volatility-analysis-container",
                    "symbol": stock_symbol
                },
                children=[
                    html.P("点击'开始分析'按钮进行波动率分析", className="text-muted")
                ],
                className="mb-4"
            )
        ])
    ])
    cards.append(analysis_card)
    
    # 尝试获取已有的波动率分析数据显示
    try:
        
        # 获取卡片摘要数据
        volatility_summary = get_stock_volatility_card_data(stock_symbol)
        if volatility_summary:
            # 从MongoDB获取详细的分析数据以显示各频率指标
            detailed_data = None
            try:
                from quantify.dashview.volatility_analysis import get_analysis_api
                api_response = get_analysis_api(stock_symbol, load_from_mongodb=True)
                if api_response and api_response.get('status') == 'success':
                    # 使用raw_data而不是data，因为_render_frequency_indicators期望原始数据结构
                    detailed_data = api_response.get('raw_data', {})
            except Exception as e:
                print(f"从MongoDB读取详细分析数据失败: {e}")
            
            summary_card = dbc.Card([
                dbc.CardHeader(html.H5("分析摘要")),
                dbc.CardBody([
                    # 总体推荐行
                    dbc.Row([
                        dbc.Col([
                            html.H6("总体推荐", className="text-primary"),
                            html.H4(volatility_summary['overall_recommendation'], 
                                   style={"color": "#28a745" if "做多" in volatility_summary['overall_recommendation'] 
                                         else "#dc3545" if "做空" in volatility_summary['overall_recommendation']
                                         else "#ffc107"})
                        ], width=4),
                        dbc.Col([
                            html.H6("告警数量", className="text-warning"),
                            html.H4(f"{volatility_summary['alert_count']} 个", 
                                   style={"color": "#dc3545" if volatility_summary['alert_count'] >= 3
                                         else "#ffc107" if volatility_summary['alert_count'] >= 1
                                         else "#28a745"})
                        ], width=4),
                        dbc.Col([
                            html.H6("分析时间", className="text-info"),
                            html.P(volatility_summary.get('analysis_date', '未知'), className="mb-0")
                        ], width=4)
                    ], className="mb-3"),
                    
                    # 频率指标详细信息行
                    html.Hr(),
                    html.H6("各频率指标最新值与历史百分位", className="text-info mb-3"),
                    _render_frequency_indicators(detailed_data) if detailed_data else html.P("暂无详细指标数据", className="text-muted")
                ])
            ], className="mb-4")
            cards.append(summary_card)
            
        # 获取图表数据
        charts = get_volatility_charts(stock_symbol)
        if charts:
            charts_card = dbc.Card([
                dbc.CardHeader(html.H5("已生成的分析图表")),
                dbc.CardBody([
                    dbc.Row([
                        dbc.Col([
                            html.H6(chart_name),
                            html.Img(
                                src=chart_data,
                                style={"width": "100%", "cursor": "pointer"},
                                id={
                                    "type": "volatility-chart-image",
                                    "indicator": chart_name,
                                    "symbol": stock_symbol
                                },
                                n_clicks=0
                            )
                        ], width=6) for chart_name, chart_data in charts.items()
                    ])
                ])
            ], className="mb-4")
            cards.append(charts_card)
            
    except Exception as e:
        print(f"获取波动率分析数据失败: {e}")
    
    return html.Div(cards)

def _render_frequency_indicators(detailed_data):
    """
    渲染频率指标的最新值和历史百分位数
    
    Args:
        detailed_data: 批量分析报告数据
        
    Returns:
        html.Div: 包含频率指标信息的组件
    """
    if not detailed_data or 'individual_analyses' not in detailed_data:
        return html.P("无法获取指标详细数据", className="text-muted")
    
    # 需要显示的4个频率指标
    target_indicators = ['90D_return', '30D_return', '90D_volatility', '30D_volatility']
    
    # 创建指标卡片
    indicator_cards = []
    
    for indicator in target_indicators:
        if indicator in detailed_data['individual_analyses']:
            analysis = detailed_data['individual_analyses'][indicator]
            # 适配新的数据结构：使用trading_advice而不是trading_analysis
            trading_advice = analysis.get('trading_advice', {})

            # 获取当前值、百分位数和位置信号
            current_value = trading_advice.get('current_value', 0)
            percentile = trading_advice.get('current_percentile', 50)
            position_desc = trading_advice.get('position_desc', '未知位置')
            position_signal = trading_advice.get('position_signal', '中性')
            
            # 根据position_signal确定颜色和样式
            if position_signal == "极端看多":
                color = "#00ff00"  # 亮绿色 - 极端看多
                color_class = "text-success"
                border_style = "3px solid"
                glow_effect = "0 0 10px #00ff0050"
            elif position_signal == "强烈看多":
                color = "#28a745"  # 深绿色 - 强烈看多  
                color_class = "text-success"
                border_style = "2px solid"
                glow_effect = "0 0 8px #28a74550"
            elif position_signal == "看多":
                color = "#20c997"  # 青绿色 - 看多
                color_class = "text-success"
                border_style = "2px solid"
                glow_effect = "0 0 6px #20c99750"
            elif position_signal == "轻微看多":
                color = "#6f42c1"  # 紫色 - 轻微看多
                color_class = "text-info"
                border_style = "1px solid"
                glow_effect = "0 0 4px #6f42c150"
            elif position_signal == "中性":
                color = "#6c757d"  # 灰色 - 中性
                color_class = "text-secondary"
                border_style = "1px solid"
                glow_effect = "none"
            elif position_signal == "轻微看空":
                color = "#fd7e14"  # 橙色 - 轻微看空
                color_class = "text-warning"
                border_style = "1px solid"
                glow_effect = "0 0 4px #fd7e1450"
            elif position_signal == "看空":
                color = "#e83e8c"  # 粉红色 - 看空
                color_class = "text-danger"
                border_style = "2px solid"
                glow_effect = "0 0 6px #e83e8c50"
            elif position_signal == "强烈看空":
                color = "#dc3545"  # 深红色 - 强烈看空
                color_class = "text-danger"
                border_style = "2px solid"
                glow_effect = "0 0 8px #dc354550"
            elif position_signal == "极端看空":
                color = "#ff0000"  # 亮红色 - 极端看空
                color_class = "text-danger"
                border_style = "3px solid"
                glow_effect = "0 0 10px #ff000050"
            else:
                # 默认情况，使用原有的百分位数逻辑
                if percentile <= 20:
                    color = "#28a745"
                    color_class = "text-success"
                    border_style = "1px solid"
                    glow_effect = "none"
                elif percentile >= 80:
                    color = "#dc3545"
                    color_class = "text-danger"
                    border_style = "1px solid"
                    glow_effect = "none"
                else:
                    color = "#ffc107"
                    color_class = "text-warning"
                    border_style = "1px solid"
                    glow_effect = "none"
            
            # 格式化显示值
            if 'return' in indicator:
                # 收益率显示为百分比
                display_value = f"{current_value*100:.2f}%"
                indicator_name = indicator.replace('_return', ' 收益率')
            else:
                # 波动率显示为百分比
                display_value = f"{current_value*100:.2f}%"
                indicator_name = indicator.replace('_volatility', ' 波动率')
            
            # 创建指标卡片
            indicator_card = dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H6(indicator_name, className="card-title mb-2", style={"fontSize": "14px"}),
                        html.H5(display_value, className=f"mb-1 {color_class}", style={"fontWeight": "bold"}),
                        html.Small([
                            html.Span(f"百分位: {percentile:.1f}%", className="mr-2"),
                            html.Span(f"({position_desc})", style={"color": color, "fontWeight": "bold"}),
                            html.Br(),
                            html.Span(f"信号: {position_signal}", style={
                                "color": color, 
                                "fontWeight": "bold", 
                                "fontSize": "12px",
                                "textShadow": "0 0 3px currentColor" if glow_effect != "none" else "none"
                            })
                        ], className="text-muted")
                    ], style={"padding": "12px"})
                ], style={
                    "border": f"{border_style} {color}", 
                    "borderRadius": "8px",
                    "boxShadow": glow_effect,
                    "transition": "all 0.3s ease",
                    "background": f"linear-gradient(135deg, rgba(255,255,255,0.05), rgba({int(color[1:3], 16)},{int(color[3:5], 16)},{int(color[5:7], 16)},0.1))" if color.startswith('#') else "rgba(255,255,255,0.05)"
                })
            ], width=3, className="mb-2")
            
            indicator_cards.append(indicator_card)
    
    if not indicator_cards:
        return html.P("暂无可显示的指标数据", className="text-muted")
    
    return dbc.Row(indicator_cards)

# 修改技术指标中的图表显示
def render_technical_indicators(data, stock_data):
    """渲染技术指标部分"""
    # 确保data是字典类型
    if not data or not isinstance(data, dict):
        return html.P("无技术指标数据")
        
    # 确保stock_data是字典类型
    if not stock_data or not isinstance(stock_data, dict):
        stock_data = {}
        
    cards = []
    
    # 均线分析卡片
    ma_data = data.get('ma', {})
    if ma_data and isinstance(ma_data, dict):
        # 获取均线分析结果
        ma_analysis = data.get('ma_analysis', {})
        if not isinstance(ma_analysis, dict):
            ma_analysis = {'ma_values': {}, 'signals': [], 'colors': {}}
        
        # 获取历史告警数据，确保安全访问
        ma_currency = stock_data.get('ma_currency', {})
        if not isinstance(ma_currency, dict):
            ma_currency = {}
            
        # 安全获取历史数据
        try:
            ma_history = db_ops.get_ma_history(stock_data.get('stock_symbol', ''))
        except Exception:
            ma_history = None
            
        try:
            boll_history = db_ops.get_boll_history(stock_data.get('stock_symbol', ''))
        except Exception:
            boll_history = None
        
        # 创建均线趋势图
        ma_fig = go.Figure()
        # 添加均线数据
        ma_values = ma_analysis.get('ma_values', {})
        ma_colors = ma_analysis.get('colors', {})
        
        if isinstance(ma_values, dict) and isinstance(ma_colors, dict):
            for ma_name, value in ma_values.items():
                if not isinstance(value, (int, float)):
                    continue
                color = ma_colors.get(ma_name, '#ffffff')  # 默认白色
                ma_fig.add_trace(go.Scatter(
                    x=[1],
                    y=[value],
                    name=ma_name,
                    line=dict(color=color),
                    mode='lines+markers'
                ))
        
        ma_fig.update_layout(
            title="均线系统分析",
            showlegend=True,
            paper_bgcolor='rgba(0,0,0,0)',
            plot_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            height=200,
            margin=dict(l=20, r=20, t=40, b=20)
        )
        
        # 创建告警历史表格
        alert_tables = []
        if ma_currency and 'alerts' in ma_currency:
            # 多头排列告警
            if ma_currency['alerts']['bullish']:
                alert_tables.append(html.Div([
                    html.Div([
                        html.H6("多头排列告警历史", className="mt-3 d-inline-block"),
                        dbc.Button(
                            "显示全部", 
                            id={
                                "type": "expand-alert-button",
                                "alert_type": "bullish",
                                "stock_id": stock_data['stock_symbol']
                            },
                            color="link",
                            size="sm",
                            className="ms-2"
                        ),
                    ], className="d-flex align-items-center"),
                    html.Div(
                        dbc.Table([
                            html.Thead(html.Tr([
                                html.Th("日期"),
                                html.Th("价格"),
                                html.Th("MA5"),
                                html.Th("MA10"),
                                html.Th("MA20")
                            ])),
                            html.Tbody([
                                html.Tr([
                                    html.Td(alert['date']),
                                    html.Td(f"{alert['price']:.2f}"),
                                    html.Td(f"{alert['ma5']:.2f}"),
                                    html.Td(f"{alert['ma10']:.2f}"),
                                    html.Td(f"{alert['ma20']:.2f}")
                                ]) for alert in ma_currency['alerts']['bullish'][-5:]  # 只显示最近5条
                            ])
                        ], bordered=True, hover=True, size="sm"),
                        id={
                            "type": "alert-table-container",
                            "alert_type": "bullish",
                            "stock_id": stock_data['stock_symbol']
                        }
                    ),
                    # 存储完整数据的隐藏组件
                    dcc.Store(
                        id={
                            "type": "alert-full-data",
                            "alert_type": "bullish",
                            "stock_id": stock_data['stock_symbol']
                        },
                        data=ma_currency['alerts']['bullish']
                    )
                ]))
            
            # 空头排列告警
            if ma_currency['alerts']['bearish']:
                alert_tables.append(html.Div([
                    html.Div([
                        html.H6("空头排列告警历史", className="mt-3 d-inline-block"),
                        dbc.Button(
                            "显示全部", 
                            id={
                                "type": "expand-alert-button",
                                "alert_type": "bearish",
                                "stock_id": stock_data['stock_symbol']
                            },
                            color="link",
                            size="sm",
                            className="ms-2"
                        ),
                    ], className="d-flex align-items-center"),
                    html.Div(
                        dbc.Table([
                            html.Thead(html.Tr([
                                html.Th("日期"),
                                html.Th("价格"),
                                html.Th("MA5"),
                                html.Th("MA10"),
                                html.Th("MA20")
                            ])),
                            html.Tbody([
                                html.Tr([
                                    html.Td(alert['date']),
                                    html.Td(f"{alert['price']:.2f}"),
                                    html.Td(f"{alert['ma5']:.2f}"),
                                    html.Td(f"{alert['ma10']:.2f}"),
                                    html.Td(f"{alert['ma20']:.2f}")
                                ]) for alert in ma_currency['alerts']['bearish'][-5:]  # 只显示最近5条
                            ])
                        ], bordered=True, hover=True, size="sm"),
                        id={
                            "type": "alert-table-container",
                            "alert_type": "bearish",
                            "stock_id": stock_data['stock_symbol']
                        }
                    ),
                    # 存储完整数据的隐藏组件
                    dcc.Store(
                        id={
                            "type": "alert-full-data",
                            "alert_type": "bearish",
                            "stock_id": stock_data['stock_symbol']
                        },
                        data=ma_currency['alerts']['bearish']
                    )
                ]))
            
            # 价格穿越告警
            for ma_name, alerts in ma_currency['alerts']['price_cross'].items():
                if alerts:
                    alert_tables.append(html.Div([
                        html.Div([
                            html.H6(f"价格穿越{ma_name}告警历史", className="mt-3 d-inline-block"),
                            dbc.Button(
                                "显示全部", 
                                id={
                                    "type": "expand-alert-button",
                                    "alert_type": f"price_cross_{ma_name}",
                                    "stock_id": stock_data['stock_symbol']
                                },
                                color="link",
                                size="sm",
                                className="ms-2"
                            ),
                        ], className="d-flex align-items-center"),
                        html.Div(
                            dbc.Table([
                                html.Thead(html.Tr([
                                    html.Th("日期"),
                                    html.Th("类型"),
                                    html.Th("价格"),
                                    html.Th(f"{ma_name}值"),
                                    html.Th(f"boll差值比"),
                                    html.Th(f"boll百分比"),
                                    html.Th(f"20-60差值"),
                                    html.Th(f"20-60百分比")
                                ])),
                                html.Tbody([
                                    html.Tr([
                                        html.Td(alert['date']),
                                        html.Td(html.Span("向上", className="text-success") if alert['type'] == 'up' else html.Span("向下", className="text-danger")),
                                        html.Td(f"{alert['price']:.2f}"),
                                        html.Td(f"{alert['ma_value']:.2f}"),
                                        html.Td(f"{alert['boll_wide']:.2f}"),
                                        html.Td(f"{alert['boll_wide_percent'] * 100:.2f}%"),
                                        html.Td(f"{alert['ma20-ma60']:.2f}"),
                                        html.Td(f"{alert['20-60_percent'] * 100:.2f}%")
                                    ]) for alert in alerts[-5:]  # 只显示最近5条
                                ])
                            ], bordered=True, hover=True, size="sm"),
                            id={
                                "type": "alert-table-container",
                                "alert_type": f"price_cross_{ma_name}",
                                "stock_id": stock_data['stock_symbol']
                            }
                        ),
                        # 存储完整数据的隐藏组件
                        dcc.Store(
                            id={
                                "type": "alert-full-data",
                                "alert_type": f"price_cross_{ma_name}",
                                "stock_id": stock_data['stock_symbol']
                            },
                            data=alerts
                        )
                    ]))
        
        # 添加统计信息
        if ma_currency and 'statistics' in ma_currency:
            stats = ma_currency['statistics']
            alert_tables.append(html.Div([
                html.H6("告警统计", className="mt-3"),
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H4(str(stats['total_alerts']), className="text-primary"),
                                html.P("总告警数", className="mb-0")
                            ])
                        ], className="mb-2")
                    ], width=3),
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H4(str(stats['bullish_alerts']), className="text-success"),
                                html.P("多头告警", className="mb-0")
                            ])
                        ], className="mb-2")
                    ], width=3),
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H4(str(stats['bearish_alerts']), className="text-danger"),
                                html.P("空头告警", className="mb-0")
                            ])
                        ], className="mb-2")
                    ], width=3),
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H4(str(stats['price_cross_alerts']), className="text-warning"),
                                html.P("穿越告警", className="mb-0")
                            ])
                        ], className="mb-2")
                    ], width=3)
                ])
            ]))
            
        # 先创建ma_card
        ma_card = dbc.Card([
            dbc.CardHeader(html.H5("均线分析")),
            dbc.CardBody([
                create_gauge_chart(ma_fig, height="200px"),
                html.Div([
                    html.P(signal['message'], 
                          className=f"text-{signal['type']}")
                    for signal in ma_analysis['signals']
                ], className="mt-3"),
                html.Div([
                    html.P([
                        html.Strong(f"{ma_name}: "),
                        html.Span(f"{value:.2f}", className="text-info")
                    ], className="mb-1")
                    for ma_name, value in ma_analysis['ma_values'].items()
                ], className="mt-3"),
                html.Div(alert_tables, className="mt-3")
            ])
        ], className="mb-4")
        
        # 在ma_card创建后，添加均线历史数据图表
        if isinstance(ma_history, pd.DataFrame) and not ma_history.empty:
            # 创建均线历史图表
            ma_history_fig = go.Figure()
            
            # 确保timestamp列被转换为datetime并仅显示年月日
            ma_history['date'] = pd.to_datetime(ma_history['timestamp'] * 1000 * 1000)
            # 创建格式化的日期列用于显示
            ma_history['date_str'] = ma_history['date'].dt.strftime('%Y%m%d')
            
            # # 对数据按日期排序
            # ma_history = ma_history.sort_values('date')
            
            # 定义均线颜色映射
            ma_colors = {
                'ma5': '#FF4444',    # 红色
                'ma10': '#D4A017',   # 深金色 (替换刺眼的黄色)
                'ma20': '#2E8B57',   # 深海绿色 (替换原来的亮绿色)
                'ma30': '#1E90FF',   # 蓝色
                'ma60': '#9370DB'    # 紫色
            }
            
            # 为每个均线添加一条线
            for ma_name, color in ma_colors.items():
                if ma_name in ma_history.columns:
                    ma_history_fig.add_trace(go.Scatter(
                        x=ma_history['date'],
                        y=ma_history[ma_name],
                        name=ma_name.upper(),
                        line=dict(color=color, width=2),
                        mode='lines'
                    ))
            
            # 设置日期格式和间隔
            # 根据数据量确定合适的日期间隔
            if len(ma_history) > 60:
                tick_interval = len(ma_history) // 10  # 显示约10个刻度
            else:
                tick_interval = 5  # 少量数据时显示更多刻度
            
            # 获取要显示的日期索引
            tick_indices = list(range(0, len(ma_history), tick_interval))
            if len(ma_history) - 1 not in tick_indices:
                tick_indices.append(len(ma_history) - 1)  # 确保显示最后一个日期
            
            # 设置图表布局 - 关键修改在这里
            ma_history_fig.update_layout(
                title="均线历史走势",
                xaxis=dict(
                    title="日期",
                    tickangle=45,
                    rangeslider=dict(visible=True),
                    type='date',
                    autorange=True,
                    nticks=20,  # 增加刻度数量到20个
                    tickmode="auto",
                    tickformatstops=[
                        dict(dtickrange=[None, 86400000], value="%Y-%m-%d"),
                        dict(dtickrange=[86400000, 604800000], value="%m-%d"),
                        dict(dtickrange=[604800000, "M1"], value="%m-%d"),
                        dict(dtickrange=["M1", "M12"], value="%Y-%m"),
                        dict(dtickrange=["M12", None], value="%Y")
                    ]
                ),
                yaxis=dict(
                    title="价格", 
                    fixedrange=False,
                    autorange=True,
                    tickmode="auto",
                    nticks=10,
                    rangemode="normal",        # 使用normal而不是auto
                    constrain="domain",      # 限制在当前域内
                    automargin=True,         # 自动调整边距
                    scaleanchor="x",         # 与X轴关联缩放
                    scaleratio=1,            # 缩放比例
                ),
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="center", x=0.5),
                showlegend=True,
                paper_bgcolor='rgba(0,0,0,0)',
                plot_bgcolor='rgba(0,0,0,0)',
                font=dict(color='white'),
                height=400,
                margin=dict(l=20, r=20, t=40, b=60),
                dragmode='zoom',
                modebar=dict(orientation='v'),
                hovermode='closest',
                uirevision='true',           # 使用固定值保持用户界面状态但允许数据更新
            )
            
            # 在Graph配置中修改自动缩放选项
            ma_history_chart = html.Div(
                dcc.Graph(
                    figure=ma_history_fig,
                    config={
                        'displayModeBar': True,
                        'scrollZoom': True,
                        'modeBarButtonsToAdd': [
                            'select2d', 
                            'lasso2d',
                            'resetScale2d',
                            'autoScale2d'
                        ],
                        'responsive': True,
                        'displaylogo': False,
                        'doubleClick': 'reset+autosize',
                        'showAxisDragHandles': True,
                        'showAxisRangeEntryBoxes': True,
                        'plotGlPixelRatio': 2,      # 提高图表分辨率
                        'autosizable': True,        # 允许自动调整大小
                        'editable': True            # 允许编辑
                    },
                    style={'height': '100%', 'width': '100%'}
                ),
                className="chart-container",
                style={'height': "400px"}
            )
            
            # 将历史图表直接插入到卡片内容的开头
            ma_card.children[1].children.insert(0, ma_history_chart)
        
        cards.append(ma_card)
    
    # BOLL通道分析卡片
    if ma_data:
        # 获取BOLL分析结果
        boll_analysis = analyze_boll_signals(ma_data)
        
        # 创建BOLL历史图表
        if isinstance(boll_history, pd.DataFrame) and not boll_history.empty:
            # 创建布林带历史图表
            boll_history_fig = go.Figure()
            
            # 确保timestamp列被转换为datetime并仅显示年月日
            boll_history['date'] = pd.to_datetime(boll_history['timestamp'] * 1000 * 1000)
            # 创建格式化的日期列用于显示
            boll_history['date_str'] = boll_history['date'].dt.strftime('%Y%m%d')
            
            # 定义布林带颜色
            boll_colors = {
                'boll_up': '#FF4444',    # 红色 - 上轨
                'boll_down': '#2E8B57',  # 深海绿色 - 下轨 (替换原来的亮绿色)
                'ma20': '#D4A017',       # 深金色 - 中轨 (替换刺眼的黄色)
                'close': '#1E90FF'       # 蓝色 - 收盘价
            }
            
            # 为布林带和收盘价添加线条
            for line_name, color in boll_colors.items():
                if line_name in boll_history.columns:
                    boll_history_fig.add_trace(go.Scatter(
                        x=boll_history['date'],
                        y=boll_history[line_name],
                        name=line_name.upper(),
                        line=dict(color=color, width=2),
                        mode='lines'
                    ))
            
            # 设置日期格式和间隔
            # 根据数据量确定合适的日期间隔
            if len(boll_history) > 60:
                tick_interval = len(boll_history) // 10  # 显示约10个刻度
            else:
                tick_interval = 5  # 少量数据时显示更多刻度
            
            # 获取要显示的日期索引
            tick_indices = list(range(0, len(boll_history), tick_interval))
            if len(boll_history) - 1 not in tick_indices:
                tick_indices.append(len(boll_history) - 1)  # 确保显示最后一个日期
            
            # 设置图表布局 - 与均线图表保持一致的配置
            boll_history_fig.update_layout(
                title="布林带历史走势",
                xaxis=dict(
                    title="日期",
                    tickangle=45,
                    rangeslider=dict(visible=True),
                    type='date',
                    autorange=True,
                    nticks=20,  # 增加刻度数量
                    tickmode="auto",
                    tickformatstops=[
                        dict(dtickrange=[None, 86400000], value="%Y-%m-%d"),
                        dict(dtickrange=[86400000, 604800000], value="%m-%d"),
                        dict(dtickrange=[604800000, "M1"], value="%m-%d"),
                        dict(dtickrange=["M1", "M12"], value="%Y-%m"),
                        dict(dtickrange=["M12", None], value="%Y")
                    ]
                ),
                yaxis=dict(
                    title="价格", 
                    fixedrange=False,
                    autorange=True,
                    tickmode="auto",
                    nticks=10,
                    rangemode="normal",        # 使用normal而不是auto
                    constrain="domain",      # 限制在当前域内
                    automargin=True,         # 自动调整边距
                    scaleanchor="x",         # 与X轴关联缩放
                    scaleratio=1,            # 缩放比例
                ),
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="center", x=0.5),
                showlegend=True,
                paper_bgcolor='rgba(0,0,0,0)',
                plot_bgcolor='rgba(0,0,0,0)',
                font=dict(color='white'),
                height=400,
                margin=dict(l=20, r=20, t=40, b=60),
                dragmode='zoom',
                modebar=dict(orientation='v'),
                hovermode='closest',
                uirevision='true',           # 使用固定值保持用户界面状态但允许数据更新
            )
            
            # 在Graph配置中修改自动缩放选项
            boll_history_chart = html.Div(
                dcc.Graph(
                    figure=boll_history_fig,
                    config={
                        'displayModeBar': True,
                        'scrollZoom': True,
                        'modeBarButtonsToAdd': [
                            'select2d', 
                            'lasso2d',
                            'resetScale2d',
                            'autoScale2d'
                        ],
                        'responsive': True,
                        'displaylogo': False,
                        'doubleClick': 'reset+autosize',
                        'showAxisDragHandles': True,
                        'showAxisRangeEntryBoxes': True,
                        'plotGlPixelRatio': 2,      # 提高图表分辨率
                        'autosizable': True,        # 允许自动调整大小
                        'editable': True            # 允许编辑
                    },
                    style={'height': '100%', 'width': '100%'}
                ),
                className="chart-container",
                style={'height': "400px"}
            )
        
        # 创建BOLL通道图表
        boll_fig = go.Figure()
        
        # 添加三条轨道线
        boll_fig.add_trace(go.Scatter(
            x=[1],
            y=[boll_analysis['bands']['upper']],
            name='上轨',
            line=dict(color='#FF4444'),
            mode='lines+markers'
        ))
        boll_fig.add_trace(go.Scatter(
            x=[1],
            y=[boll_analysis['bands']['middle']],
            name='中轨(MA20)',
            line=dict(color='#D4A017'),
            mode='lines+markers'
        ))
        boll_fig.add_trace(go.Scatter(
            x=[1],
            y=[boll_analysis['bands']['lower']],
            name='下轨',
            line=dict(color='#2E8B57'),
            mode='lines+markers'
        ))
        
        boll_fig.update_layout(
            title="BOLL通道分析",
            showlegend=True,
            paper_bgcolor='rgba(0,0,0,0)',
            plot_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            height=200,
            margin=dict(l=20, r=20, t=40, b=20)
        )
        
        boll_card = dbc.Card([
            dbc.CardHeader(html.H5("BOLL通道分析")),
            dbc.CardBody([
                # 添加布林带历史图表到卡片顶部
                boll_history_chart if (isinstance(boll_history, pd.DataFrame) and not boll_history.empty) else None,
                create_gauge_chart(boll_fig, height="200px"),
                html.Div([
                    html.P([
                        html.Strong(signal['title'] + "："),
                        html.Span(signal['message'], 
                                className=f"text-{signal['type']}")
                    ])
                    for signal in boll_analysis['signals']
                ], className="mt-3"),
                html.Div([
                    html.P([
                        html.Strong("上轨: "),
                        html.Span(f"{boll_analysis['bands']['upper']:.2f}", 
                                className="text-danger")
                    ], className="mb-1"),
                    html.P([
                        html.Strong("中轨: "),
                        html.Span(f"{boll_analysis['bands']['middle']:.2f}", 
                                className="text-warning")
                    ], className="mb-1"),
                    html.P([
                        html.Strong("下轨: "),
                        html.Span(f"{boll_analysis['bands']['lower']:.2f}", 
                                className="text-success")
                    ], className="mb-1"),
                    html.P([
                        html.Strong("带宽: "),
                        html.Span(f"{boll_analysis['bands']['bandwidth']:.2f}%",
                                className=f"text-{'danger' if boll_analysis['bandwidth_status'] == 'wide' else 'warning' if boll_analysis['bandwidth_status'] == 'narrow' else 'info'}")
                    ], className="mb-1")
                ], className="mt-3")
            ])
        ], className="mb-4")
        cards.append(boll_card)
    
    # MACD指标卡片
    macd_data = data.get('macd', {})
    if macd_data and isinstance(macd_data, dict):
        # 确保所有需要的字段都存在且类型正确
        dif_value = macd_data.get('DIF')
        dea_value = macd_data.get('DEA')
        macd_value = macd_data.get('MACD')
        macd_trend = macd_data.get('trend', '')
        
        # 确保数值类型正确
        if not isinstance(dif_value, (int, float)):
            dif_value = 0
        if not isinstance(dea_value, (int, float)):
            dea_value = 0
        if not isinstance(macd_value, (int, float)):
            macd_value = 0
        
        # 创建MACD图表
        macd_fig = go.Figure()
        macd_fig.add_trace(go.Bar(
            name='MACD',
            x=['MACD'],
            y=[macd_value],
            marker_color='#FF4444' if macd_value > 0 else '#2E8B57'
        ))
        macd_fig.add_trace(go.Scatter(
            name='DIF',
            x=['MACD'],
            y=[dif_value],
            mode='markers',
            marker=dict(size=10, color='#D4A017')
        ))
        macd_fig.add_trace(go.Scatter(
            name='DEA',
            x=['MACD'],
            y=[dea_value],
            mode='markers',
            marker=dict(size=10, color='purple')
        ))
        
        macd_fig.update_layout(
            title="MACD指标分析",
            showlegend=True,
            paper_bgcolor='rgba(0,0,0,0)',
            plot_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            height=200,
            margin=dict(l=20, r=20, t=40, b=20)
        )
        
        # MACD分析
        macd_analysis = []
        if dif_value > dea_value:
            macd_analysis.append(html.P("MACD金叉形成，可能出现上涨行情", className="text-success"))
        else:
            macd_analysis.append(html.P("MACD死叉形成，注意下跌风险", className="text-danger"))
            
        if macd_value > 0:
            macd_analysis.append(html.P("MACD柱线为正，多头市场", className="text-success"))
        else:
            macd_analysis.append(html.P("MACD柱线为负，空头市场", className="text-danger"))
        
        macd_card = dbc.Card([
            dbc.CardHeader(html.H5("MACD指标")),
            dbc.CardBody([
                create_gauge_chart(macd_fig, height="200px"),
                html.Div(macd_analysis, className="mt-3"),
                html.Div([
                    html.P([
                        html.Strong("DIF: "),
                        html.Span(f"{dif_value:.4f}", className="text-info")
                    ], className="mb-1"),
                    html.P([
                        html.Strong("DEA: "),
                        html.Span(f"{dea_value:.4f}", className="text-info")
                    ], className="mb-1"),
                    html.P([
                        html.Strong("MACD: "),
                        html.Span(f"{macd_value:.4f}", 
                                className=f"text-{'success' if macd_value > 0 else 'danger'}")
                    ], className="mb-1"),
                    html.P([
                        html.Strong("趋势: "),
                        html.Span(macd_trend, 
                                className=f"text-{'success' if macd_trend == '上升' else 'danger'}")
                    ])
                ])
            ])
        ], className="mb-4")
        cards.append(macd_card)
    
    # 交易信号卡片
    signals_data = data.get('signals', {})
    if signals_data and isinstance(signals_data, dict):
        trading_signals = signals_data.get('trading_signals', {})
        if not isinstance(trading_signals, dict):
            trading_signals = {}
            
        signal_items = []
        
        # 操作建议
        buy_signal = trading_signals.get('buy', {})
        if isinstance(buy_signal, dict):
            signal_items.append(dbc.Alert([
                html.H6("买入信号", className="alert-heading"),
                html.P([
                    html.Strong("信号强度: ", className="text-success"),
                    html.Span(buy_signal.get('strength', ''), className="text-success")
                ]),
                html.P([
                    html.Strong("分析依据: "),
                    html.Span(buy_signal.get('reason', ''))
                ])
            ], color="success", className="mb-3"))
        
        sell_signal = trading_signals.get('sell', {})
        if isinstance(sell_signal, dict):
            signal_items.append(dbc.Alert([
                html.H6("卖出信号", className="alert-heading"),
                html.P([
                    html.Strong("信号强度: ", className="text-danger"),
                    html.Span(sell_signal.get('strength', ''), className="text-danger")
                ]),
                html.P([
                    html.Strong("分析依据: "),
                    html.Span(sell_signal.get('reason', ''))
                ])
            ], color="danger", className="mb-3"))
        
        risk_signal = trading_signals.get('risk', {})
        if isinstance(risk_signal, dict):
            risk_level = risk_signal.get('level', '低')
            risk_color = {
                "高": "danger",
                "中": "warning",
                "低": "success"
            }.get(risk_level, "info")
            
            signal_items.append(dbc.Alert([
                html.H6("风险提示", className="alert-heading"),
                html.P([
                    html.Strong("风险等级: "),
                    html.Span(risk_level, className=f"text-{risk_color}")
                ]),
                html.P([
                    html.Strong("风险分析: "),
                    html.Span(risk_signal.get('description', ''))
                ])
            ], color=risk_color))
        
        if signal_items:
            signal_card = dbc.Card([
                dbc.CardHeader(html.H5("交易信号分析")),
                dbc.CardBody(signal_items)
            ], className="mb-4")
            cards.append(signal_card)
    
    return dbc.Row([
        dbc.Col(
            card, 
            md=6,
            style={
                "marginBottom": "15px"  # 添加卡片间距
            }
        ) for card in cards
    ])

# 相对分析告警检测函数
def analyze_relative_analysis_alerts(stocks_data):
    """
    分析股票相对分析中的百分位数据，检测过高/过低告警
    
    Args:
        stocks_data (dict): 股票数据字典
        
    Returns:
        list: 相对分析告警列表
    """
    alerts = []
    
    # 告警阈值设定
    HIGH_PERCENTILE_THRESHOLD = 85  # 超额收益百分位 > 85% 为过高
    LOW_PERCENTILE_THRESHOLD = 15   # 超额收益百分位 < 15% 为过低
    
    try:
        for stock_id, stock_data in stocks_data.items():
            if not isinstance(stock_data, dict):
                continue
                
            stock_symbol = stock_data.get('stock_symbol', '')
            stock_name = stock_data.get('stock_name', '')
            relative_analysis = stock_data.get('relative_analysis', {})
            
            if not relative_analysis or not isinstance(relative_analysis, dict):
                continue
                
            # 分析每个时间周期的相对表现
            alert_details = []
            max_severity = 'low'
            
            for period, values in relative_analysis.items():
                if not isinstance(values, dict):
                    continue
                    
                excess_return = values.get('excess_return')
                excess_return_percentile = values.get('excess_return_percentile')
                
                if excess_return is None or excess_return_percentile is None:
                    continue
                    
                percentile_value = excess_return_percentile * 100  # 转换为百分比
                
                # 检测告警条件
                alert_type = None
                severity = 'low'
                
                if percentile_value >= HIGH_PERCENTILE_THRESHOLD:
                    alert_type = 'high_percentile'
                    severity = 'high' if percentile_value >= 95 else 'medium'
                    max_severity = severity if severity == 'high' or max_severity != 'high' else max_severity
                elif percentile_value <= LOW_PERCENTILE_THRESHOLD:
                    alert_type = 'low_percentile'
                    severity = 'high' if percentile_value <= 5 else 'medium'
                    max_severity = severity if severity == 'high' or max_severity != 'high' else max_severity
                
                if alert_type:
                    alert_details.append({
                        'period': period,
                        'excess_return': excess_return,
                        'percentile': percentile_value,
                        'alert_type': alert_type,
                        'severity': severity,
                        'message': f"{period}: 超额收益百分位 {percentile_value:.1f}%" + 
                                 (f" (超额收益 {excess_return*100:+.2f}%)" if excess_return != 0 else "")
                    })
            
            # 如果有告警，添加到结果中
            if alert_details:
                alert = {
                    'symbol': stock_symbol,
                    'name': stock_name,
                    'alert_count': len(alert_details),
                    'severity': max_severity,
                    'alert_details': alert_details,
                    'index_symbol': stock_data.get('index_symbol', ''),
                    'index_name': stock_data.get('index_name', ''),
                    'last_updated': stock_data.get('analysis_date', '')
                }
                alerts.append(alert)
        
        # 按严重程度和告警数量排序  
        alerts.sort(key=lambda x: (
            3 if x['severity'] == 'high' else 2 if x['severity'] == 'medium' else 1,
            x['alert_count']
        ), reverse=True)
        return alerts
        
    except Exception as e:
        logger.error(f"分析相对分析告警失败: {e}")
        return []

# 修改相对分析的渲染函数
def render_relative_analysis(data, stock_data):
    """渲染相对分析部分（分为数据与折线图两部分，展示全周期）"""
    logger.warning("开始进行相对分析数据渲染")
    if not data or not isinstance(data, dict):
        return html.P("无相对分析数据")

    # 基本信息
    index_symbol = stock_data.get('index_symbol', '')
    index_name = stock_data.get('index_name', '')
    stock_symbol = stock_data.get('stock_symbol', '')

    # 获取周期列表（从数据库键名解析，便于扩展）；fallback 使用 data 的键
    periods = db_ops.get_relative_analysis_periods(stock_symbol, index_symbol) if stock_symbol and index_symbol else []
    if not periods:
        periods = [p for p in data.keys() if isinstance(data.get(p), dict)]

    # 构建“展示数据”部分：每个周期一行，背景色根据百分位分级
    data_rows = []
    for period in periods:
        values = data.get(period, {}) if isinstance(data.get(period, {}), dict) else {}
        stock_return = values.get('stock_return')
        index_return = values.get('index_return')
        excess_return = values.get('excess_return')
        excess_return_percentile = values.get('excess_return_percentile')

        percentile_value = excess_return_percentile * 100 if excess_return_percentile is not None else None
        # 背景色分级（显示部分的颜色，不是数字的颜色）
        bg_style = {"backgroundColor": "rgba(108,117,125,0.15)", "border": "1px solid #6c757d"}
        if percentile_value is not None:
            if percentile_value >= 95 or percentile_value <= 5:
                bg_style = {"backgroundColor": "rgba(220,53,69,0.2)", "border": "1px solid #dc3545"}
            elif percentile_value >= 85 or percentile_value <= 15:
                bg_style = {"backgroundColor": "rgba(255,193,7,0.2)", "border": "1px solid #ffc107"}

        row = dbc.Row([
            dbc.Col(html.Div(period, className="fw-bold"), width=2),
            dbc.Col(html.Div(f"指数收益率: {index_return*100:.2f}%" if index_return is not None else "指数收益率: N/A"), width=2),
            dbc.Col(html.Div(f"标的收益率: {stock_return*100:.2f}%" if stock_return is not None else "标的收益率: N/A"), width=2),
            dbc.Col(html.Div(f"超额收益率: {excess_return*100:.2f}%" if excess_return is not None else "超额收益率: N/A"), width=3),
            dbc.Col(html.Div(f"超额收益率百分位: {percentile_value:.1f}%" if percentile_value is not None else "超额收益率百分位: N/A"), width=3),
        ], className="gy-2 gx-3 align-items-center mb-2 p-2 rounded", style=bg_style)
        data_rows.append(row)

    data_section = dbc.Card([
        dbc.CardHeader([
            html.Div([
                html.H5("相对分析数据", className="mb-0 d-inline"),
            ]),
            html.Small(f"对比指数: {index_name} ({index_symbol})", className="text-muted d-block mt-1")
        ]),
        dbc.CardBody([
            dbc.Row([
                dbc.Col(html.Div("周期", className="fw-bold text-muted"), width=2),
                dbc.Col(html.Div("指数收益率", className="fw-bold text-muted"), width=2),
                dbc.Col(html.Div("标的收益率", className="fw-bold text-muted"), width=2),
                dbc.Col(html.Div("超额收益率", className="fw-bold text-muted"), width=3),
                dbc.Col(html.Div("超额收益率百分位", className="fw-bold text-muted"), width=3),
            ], className="gy-2 gx-3 mb-2"),
            html.Div(data_rows)
        ], className="p-3")
    ], className="mb-3")

    # 构建“折线图”部分：一次展示所有周期，图表全宽，容器可滚动
    chart_items = []
    for period in periods:
        ts_df = db_ops.get_relative_analysis_timeseries(stock_symbol, index_symbol, period) if stock_symbol and index_symbol else pd.DataFrame()
        if ts_df is None or ts_df.empty:
            continue
        # 选择日期作为横轴：优先 'date' 列，其次 'timestamp' 列，最后索引
        x_axis = None
        if 'date' in ts_df.columns:
            ts_df['date'] = pd.to_datetime(ts_df['date'], errors='coerce')
            # 去时区
            try:
                ts_df['date'] = ts_df['date'].dt.tz_localize(None)
            except Exception:
                try:
                    ts_df['date'] = ts_df['date'].dt.tz_convert(None)
                except Exception:
                    pass
            ts_df['date'] = ts_df['date'].dt.floor('D')

            # 若索引名也叫 'date'，先重命名索引避免歧义
            if isinstance(ts_df.index, pd.MultiIndex):
                pass
            else:
                if ts_df.index.name == 'date':
                    ts_df = ts_df.rename_axis('idx_date')

            ts_df = ts_df.sort_values(by='date')
            x_axis = ts_df['date']
        elif 'timestamp' in ts_df.columns:
            ts_df['timestamp'] = pd.to_numeric(ts_df['timestamp'], errors='coerce')
            if ts_df['timestamp'].max() and ts_df['timestamp'].max() > 10**11:
                x_axis = pd.to_datetime(ts_df['timestamp'], unit='ms', errors='coerce')
            else:
                x_axis = pd.to_datetime(ts_df['timestamp'], unit='s', origin='unix', errors='coerce')
            try:
                x_axis = x_axis.tz_localize(None)
            except Exception:
                try:
                    x_axis = x_axis.tz_convert(None)
                except Exception:
                    pass
            x_axis = x_axis.floor('D')
            ts_df = ts_df.sort_values('timestamp')
        elif isinstance(ts_df.index, pd.DatetimeIndex):
            idx = ts_df.index
            try:
                idx = idx.tz_localize(None)
            except Exception:
                try:
                    idx = idx.tz_convert(None)
                except Exception:
                    pass
            x_axis = pd.to_datetime(idx).floor('D')
            ts_df = ts_df.sort_index()
        else:
            ts_df = ts_df.sort_index()
        ts_fig = go.Figure()
        if 'stock_return' in ts_df.columns:
            ts_fig.add_trace(go.Scatter(x=(x_axis if 'x_axis' in locals() and x_axis is not None else ts_df.index), y=ts_df['stock_return'] * 100, name='股票收益率', mode='lines', line=dict(width=2, color='#17a2b8')))
        if 'index_return' in ts_df.columns:
            ts_fig.add_trace(go.Scatter(x=(x_axis if 'x_axis' in locals() and x_axis is not None else ts_df.index), y=ts_df['index_return'] * 100, name='指数收益率', mode='lines', line=dict(width=2, color='#6c757d')))
        if 'stock_return_diff' in ts_df.columns:
            ts_fig.add_trace(go.Scatter(x=(x_axis if 'x_axis' in locals() and x_axis is not None else ts_df.index), y=ts_df['stock_return_diff'] * 100, name='超额收益', mode='lines', line=dict(width=2, color='#ffc107')))
        ts_fig.update_layout(
            title=f"{period} 相对分析时序",
            showlegend=True,
            paper_bgcolor='rgba(0,0,0,0)',
            plot_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            height=260,
            margin=dict(l=20, r=20, t=40, b=20),
            xaxis=dict(type='date')
        )
        chart_items.append(
            html.Div(create_gauge_chart(ts_fig, height="260px"), className="mb-3", style={"width": "100%"})
        )

    charts_section = dbc.Card([
        dbc.CardHeader(html.H5("相对分析折线图", className="mb-0")),
        dbc.CardBody(html.Div(chart_items), style={"maxHeight": "65vh", "overflowY": "auto"})
    ])

    # 两部分并列/纵向排列：移动端纵向，桌面端上下排列也可
    logger.warning("相对分析数据渲染完成")
    return dbc.Container([
        data_section,
        charts_section
    ], fluid=True)

# 修改动态指标分析的渲染函数
def render_dynamic_analysis(data, stock_data):
    """渲染动态分析部分"""
    if not data or not isinstance(data, dict):
        return html.P("无动态指标数据")
        
    consecutive_returns = data.get('consecutive_returns', {})
    if not consecutive_returns or not isinstance(consecutive_returns, dict):
        return html.P("无连续收益率数据")
    
    tabs_content = []
    for period, period_data in consecutive_returns.items():
        if not isinstance(period_data, dict):
                    continue
                    
        tab_content = []
        
        # 添加周期说明，使用更紧凑的布局
        period_description = {
            'daily': '日度数据',
            'weekly': '周度数据',
            'monthly': '月度数据'
        }
        
        # 创建说明卡片，使用更简洁的样式
        tab_content.append(dbc.Card([
            dbc.CardBody([
                html.Small([
                    html.Strong("指标说明："),
                    "连续次数-当前连续上涨/下跌天数；",
                    "累计回报率-连续期间累计收益；",
                    "百分位-历史分布位置；",
                    "参考值-历史平均水平"
                ], className="text-muted")
            ], className="p-2")
        ], className="mb-2"))
        
        # 处理正收益数据，使用更紧凑的布局
        positive_data = period_data.get('positive', {})
        if positive_data and isinstance(positive_data, dict):
            count_data = positive_data.get('count', {})
            return_data = positive_data.get('return', {})
            
            if not isinstance(count_data, dict):
                count_data = {}
            if not isinstance(return_data, dict):
                return_data = {}
                
            latest_count = count_data.get('latest', 0)
            count_percentile = count_data.get('percentile', 0)
            latest_return = return_data.get('latest', 0)
            mean_return = return_data.get('mean', 0)
            
            # 确保数值类型正确
            if not isinstance(latest_return, (int, float)):
                latest_return = 0
            if not isinstance(mean_return, (int, float)):
                mean_return = 0
            
            # 优化仪表盘图表尺寸
            gauge_fig = go.Figure(go.Indicator(
                mode="gauge+number",
                value=latest_return * 100,
                number={
                    'suffix': "%",
                    'font': {'size': 20}
                },
                title={
                    'text': f"累计回报率",
                    'font': {'size': 12}
                },
                gauge={
                    'axis': {'range': [0, max(latest_return, mean_return) * 150]},
                    'bar': {'color': "#28a745"},
                    'threshold': {
                        'line': {'color': "white", 'width': 2},
                        'thickness': 0.75,
                        'value': mean_return * 100
                    }
                }
            ))
            gauge_fig.update_layout(
                paper_bgcolor='rgba(0,0,0,0)',
                font={'color': "white", 'size': 12},
                height=150,  # 减小高度
                width=300,   # 限制宽度
                margin=dict(l=20, r=20, t=30, b=20)
            )
            
            # 上涨统计卡片，使用更紧凑的布局
            tab_content.append(dbc.Card([
                dbc.CardHeader([
                    html.H6("连续上涨统计", className="mb-0 small"),
                    html.Small(f"参考值: {mean_return*100:.1f}%", className="text-muted")
                ], className="py-2"),
                dbc.CardBody([
                    dbc.Row([
                        dbc.Col([
                            html.Div([
                                html.H4(f"{latest_count}天", className="text-success mb-0"),
                                html.Small("当前连续上涨", className="text-muted"),
                                dbc.Progress([
                                    dbc.Progress(
                                        f"{count_percentile*100:.0f}%",
                                        value=count_percentile*100,
                                        color="success",
                                        style={"fontSize": "0.7rem"}
                                    )
                                ], className="mt-1")
                            ])
                        ], width=4),
                        dbc.Col([
                            create_gauge_chart(gauge_fig, height="150px")
                        ], width=8)
                    ], className="g-0")
                ], className="p-2")
            ], className="mb-2"))
            
        # 处理负收益数据，使用更紧凑的布局
        negative_data = period_data.get('negative', {})
        if negative_data and isinstance(negative_data, dict):
            count_data = negative_data.get('count', {})
            return_data = negative_data.get('return', {})
            
            if not isinstance(count_data, dict):
                count_data = {}
            if not isinstance(return_data, dict):
                return_data = {}
                
            latest_count = count_data.get('latest', 0)
            count_percentile = count_data.get('percentile', 0)
            latest_return = return_data.get('latest', 0)
            mean_return = return_data.get('mean', 0)
            
            # 确保数值类型正确
            if not isinstance(latest_return, (int, float)):
                latest_return = 0
            if not isinstance(mean_return, (int, float)):
                mean_return = 0
            
            # 优化仪表盘图表尺寸
            gauge_fig = go.Figure(go.Indicator(
                mode="gauge+number",
                value=abs(latest_return * 100),
                number={
                    'suffix': "%",
                    'font': {'size': 20}
                },
                title={
                    'text': f"累计回报率",
                    'font': {'size': 12}
                },
                gauge={
                    'axis': {'range': [0, abs(min(latest_return, mean_return)) * 150]},
                    'bar': {'color': "#dc3545"},
                    'threshold': {
                        'line': {'color': "white", 'width': 2},
                        'thickness': 0.75,
                        'value': abs(mean_return * 100)
                    }
                }
            ))
            gauge_fig.update_layout(
                paper_bgcolor='rgba(0,0,0,0)',
                font={'color': "white", 'size': 12},
                height=150,  # 减小高度
                width=300,   # 限制宽度
                margin=dict(l=20, r=20, t=30, b=20)
            )
            
            # 下跌统计卡片，使用更紧凑的布局
            tab_content.append(dbc.Card([
                dbc.CardHeader([
                    html.H6("连续下跌统计", className="mb-0 small"),
                    html.Small(f"参考值: {abs(mean_return*100):.1f}%", className="text-muted")
                ], className="py-2"),
                dbc.CardBody([
                    dbc.Row([
                        dbc.Col([
                            html.Div([
                                html.H4(f"{latest_count}天", className="text-danger mb-0"),
                                html.Small("当前连续下跌", className="text-muted"),
                                dbc.Progress([
                                    dbc.Progress(
                                        f"{count_percentile*100:.0f}%",
                                        value=count_percentile*100,
                                        color="danger",
                                        style={"fontSize": "0.7rem"}
                                    )
                                ], className="mt-1")
                            ])
                        ], width=4),
                        dbc.Col([
                            create_gauge_chart(gauge_fig, height="150px")
                        ], width=8)
                    ], className="g-0")
                ], className="p-2")
            ], className="mb-2"))
        
        period_name = period_description.get(period, period)
        tabs_content.append(dbc.Tab(
            dbc.Row([dbc.Col(content, width=12) for content in tab_content]),
            label=period_name,
            tab_id=f"tab-{period}"
        ))
    
    # 使用更紧凑的标签页布局
    return dbc.Tabs(
        tabs_content,
        className="nav-justified",
        style={
            "maxWidth": "800px", 
            "margin": "0 auto",
            "maxHeight": f"calc({modal_max_height} - 210px)",  # 设置最大高度
            "overflowY": "auto"                 # 启用垂直滚动
        }
    )

# 修改宏观分析中的图表显示
def render_macro_analysis(data, stock_data):
    """渲染宏观分析部分"""
    if not data or not isinstance(data, dict):
        return html.P("无宏观分析数据")
    
    # 风险溢价仪表盘
    risk_premium = data.get('risk_premium', 0)
    risk_premium_percentile = data.get('risk_premium_percentile', 0)
    
    # 确保数值类型正确
    if not isinstance(risk_premium, (int, float)):
        risk_premium = 0
    if not isinstance(risk_premium_percentile, (int, float)):
        risk_premium_percentile = 0
    
    gauge_fig = go.Figure(go.Indicator(
        mode="gauge+number",
        value=risk_premium_percentile * 100 if risk_premium_percentile is not None else 0,
        title={'text': "风险溢价百分位"},
        gauge={
            'axis': {'range': [0, 100]},
            'bar': {'color': "#17a2b8"},
            'steps': [
                {'range': [0, 30], 'color': "green"},  # 低风险溢价区间为绿色
                {'range': [30, 70], 'color': "yellow"},  # 中等风险溢价区间为黄色
                {'range': [70, 100], 'color': "red"}  # 高风险溢价区间为红色
            ],
            'threshold': {
                'line': {'color': "white", 'width': 4},
                'thickness': 0.75,
                'value': risk_premium_percentile * 100 if risk_premium_percentile is not None else 0
            }
        }
    ))
    gauge_fig.update_layout(
        paper_bgcolor='rgba(0,0,0,0)',
        font={'color': "white"},
        height=300,
        margin=dict(l=20, r=20, t=40, b=20)
    )
    
    # 安全获取数据值，确保显示正确
    pe_value = data.get('pe', 'N/A')
    if not isinstance(pe_value, (int, float)) and pe_value != 'N/A':
        pe_value = 'N/A'
    
    pe_yield = data.get('pe_yield', 'N/A')
    if not isinstance(pe_yield, (int, float)) and pe_yield != 'N/A':
        pe_yield = 'N/A'
    else:
        pe_yield = f"{pe_yield}%" if pe_yield != 'N/A' else 'N/A'
        
    risk_free_rate = data.get('risk_free_rate', 'N/A')
    if not isinstance(risk_free_rate, (int, float)) and risk_free_rate != 'N/A':
        risk_free_rate = 'N/A'
    else:
        risk_free_rate = f"{risk_free_rate}%" if risk_free_rate != 'N/A' else 'N/A'
    
    risk_premium_display = f"{risk_premium}%" if isinstance(risk_premium, (int, float)) else 'N/A'
    
    # 创建指标卡片
    indicator_cards = [
        dbc.Card([
            dbc.CardBody([
                html.H5("市盈率", className="card-title"),
                html.H3(f"{pe_value}", className="text-info"),
                html.P(f"收益率: {pe_yield}", className="mb-0")
            ])
        ], className="mb-3"),
        dbc.Card([
            dbc.CardBody([
                html.H5("无风险利率", className="card-title"),
                html.H3(f"{risk_free_rate}", className="text-warning"),
            ])
        ], className="mb-3"),
        dbc.Card([
            dbc.CardBody([
                html.H5("风险溢价", className="card-title"),
                html.H3(f"{risk_premium_display}", 
                       className=f"text-{'success' if risk_premium and risk_premium > 0 else 'danger'}")
            ])
        ])
    ]
    
    return dbc.Row([
        dbc.Col([
            create_gauge_chart(gauge_fig, height="250px")
        ], md=6),
        dbc.Col(
            indicator_cards, 
            md=6,
            style={
                "maxHeight": f"calc({modal_max_height} - 210px)",  # 设置最大高度
                "overflowY": "auto"                 # 启用垂直滚动
            }
        )
    ])

# 添加全局变量用于在线程间共享进度信息
stock_refresh_status = {"progress": 0, "message": "", "type": "stock", "source": "refresh"}
# 添加全局变量用于在线程间共享进度信息
volatility_refresh_status = {"progress": 0, "message": "", "type": "stock", "source": "volatility_refresh"}
# 添加用于新股票分析的全局变量
new_stock_analysis_status = {"progress": 0, "message": "", "type": "stock", "source": "new_stock"}
# 全局变量用于在线程间共享进度信息
option_refresh_status = {"progress": 0, "message": "", "type": "option", "source": "option"}

# 测试回调已移除，功能正常

@app.callback(
    [
        Output("stocks-data-store", "data"),
        Output("refresh-progress-store", "data"),
        Output("refresh-progress-modal", "is_open"),
        Output("refresh-progress-interval", "disabled"),
        Output("task-queue-data-store", "data", allow_duplicate=True)  # 添加任务队列更新
    ],
    [Input("refresh-data", "n_clicks")],
    [State("stocks-data-store", "data")],
    prevent_initial_call=True
)
def refresh_data(n_clicks, current_data):
    if not n_clicks:
        return current_data, {"progress": 0, "message": ""}, False, True, dash.no_update

    # 防止重复点击：检查是否已有股票数据刷新任务在运行
    global stock_refresh_status

    # 只检查来源为 "refresh" 的任务，避免与其他功能冲突
    # 修复逻辑：只有当progress在1-99之间时才认为任务正在运行（0表示未开始，100表示已完成）
    if (stock_refresh_status and
        1 <= stock_refresh_status.get("progress", 100) < 100 and
        stock_refresh_status.get("source") == "refresh"):
        logger.info("股票数据刷新任务已在运行中，忽略重复点击")
        return current_data, stock_refresh_status, True, False, dash.no_update

    try:
        # 清理所有数据缓存，确保获取最新数据
        clear_all_caches()

        # 使用任务优化器处理IO密集型的数据刷新任务
        optimizer = get_task_optimizer()

        def refresh_task():
            # 导入异步任务管理器
            from quantify.dashview.async_task_manager import submit_refresh_stock_data, TaskPriority
            return submit_refresh_stock_data(TaskPriority.HIGH)

        # 异步提交数据刷新任务
        future = optimizer.optimize_task("data_refresh", refresh_task)
        task_id = future.result()  # 等待任务提交完成

        logger.info(f"已提交股票数据刷新任务，任务ID: {task_id}")

        # 初始化进度信息
        stock_refresh_status = {
            "progress": 5,
            "message": "已启动多进程数据刷新",
            "type": "stock",
            "source": "refresh",
            "task_id": task_id
        }

        # 获取更新后的任务队列数据
        try:
            from quantify.dashview.async_task_manager import get_task_manager
            multiprocess_manager = get_task_manager()
            updated_queue_data = {
                "current": [],
                "completed": []
            }

            # 获取多进程任务
            mp_tasks = multiprocess_manager.get_all_tasks()
            for task_id, task_info in mp_tasks.items():
                mp_task = {
                    "task_id": task_id,
                    "task_name": f"[多进程] {task_info.task_name}",
                    "task_type": task_info.task_type,
                    "status": task_info.status.value,
                    "created_time": task_info.created_time,
                    "progress": task_info.progress,
                    "details": f"进度: {task_info.progress:.1f}%"
                }
                updated_queue_data["current"].append(mp_task)
        except Exception as queue_error:
            logger.error(f"获取任务队列数据失败: {queue_error}")
            updated_queue_data = dash.no_update

        return current_data, stock_refresh_status, True, False, updated_queue_data

    except Exception as e:
        logger.error(f"启动多进程数据刷新失败: {str(e)}")
        return current_data, {
            "progress": 100,
            "message": f"启动失败: {str(e)}",
            "type": "stock",
            "source": "refresh"
        }, True, False, dash.no_update

# 添加多进程任务进度监控回调
@app.callback(
    [Output("refresh-progress-store", "data", allow_duplicate=True)],
    [Input("refresh-progress-interval", "n_intervals")],
    [State("refresh-progress-store", "data")],
    prevent_initial_call=True
)
def monitor_multiprocess_tasks(n_intervals, current_progress):
    """监控多进程任务进度"""
    # 检查当前进度状态或全局状态中是否有多进程任务
    global volatility_refresh_status, stock_refresh_status, new_stock_analysis_status
    global machine_analysis_status, correlation_analysis_status, basis_report_generation_status, option_comprehensive_analysis_status

    # 优先检查当前进度状态
    task_id = None
    progress_status = None

    if current_progress and current_progress.get("task_id"):
        task_id = current_progress.get("task_id")
        progress_status = current_progress
    # 检查波动率刷新状态
    elif volatility_refresh_status and volatility_refresh_status.get("task_id"):
        task_id = volatility_refresh_status.get("task_id")
        progress_status = volatility_refresh_status
    # 检查股票刷新状态
    elif stock_refresh_status and stock_refresh_status.get("task_id"):
        task_id = stock_refresh_status.get("task_id")
        progress_status = stock_refresh_status
    # 检查新股票分析状态
    elif new_stock_analysis_status and new_stock_analysis_status.get("task_id"):
        task_id = new_stock_analysis_status.get("task_id")
        progress_status = new_stock_analysis_status
    # 检查机器学习分析状态
    elif machine_analysis_status and machine_analysis_status.get("task_id"):
        task_id = machine_analysis_status.get("task_id")
        progress_status = machine_analysis_status
    # 检查相关性分析状态
    elif correlation_analysis_status and correlation_analysis_status.get("task_id"):
        task_id = correlation_analysis_status.get("task_id")
        progress_status = correlation_analysis_status
    # 检查升贴水报告生成状态
    elif basis_report_generation_status and basis_report_generation_status.get("task_id"):
        task_id = basis_report_generation_status.get("task_id")
        progress_status = basis_report_generation_status
    # 检查期权综合分析状态
    elif option_comprehensive_analysis_status and option_comprehensive_analysis_status.get("task_id"):
        task_id = option_comprehensive_analysis_status.get("task_id")
        progress_status = option_comprehensive_analysis_status

    if not task_id or not progress_status:
        return [dash.no_update]

    try:
        # 导入任务管理器
        from quantify.dashview.async_task_manager import get_task_manager

        task_manager = get_task_manager()

        # 获取任务状态
        task_info = task_manager.get_task_status(task_id)

        if not task_info:
            return [dash.no_update]

        # 更新进度信息
        updated_progress = progress_status.copy()
        updated_progress["progress"] = task_info.progress

        # 根据任务状态更新消息
        if task_info.status.value == "running":
            updated_progress["message"] = f"多进程任务执行中... ({task_info.progress:.1f}%)"
        elif task_info.status.value == "completed":
            updated_progress["progress"] = 100
            updated_progress["message"] = "多进程任务完成"

            # 如果是数据刷新任务，将结果存储到进度状态中
            if task_info.task_type == "refresh_stock_data" and task_info.result:
                result = task_info.result
                if result.get("success") and result.get("updated_data"):
                    updated_progress["updated_data"] = result["updated_data"]
                    logger.info("多进程数据刷新完成，数据已存储到进度状态")

            # 如果是波动率分析任务，重新获取数据
            elif task_info.task_type in ["volatility_analysis", "batch_volatility_analysis"]:
                # 重新获取股票数据以反映波动率分析结果
                updated_stocks = db_ops.get_all_stocks()
                updated_progress["updated_data"] = updated_stocks
                logger.info("多进程波动率分析完成，数据已存储到进度状态")

        elif task_info.status.value == "failed":
            updated_progress["progress"] = 100
            updated_progress["message"] = f"多进程任务失败: {task_info.error or '未知错误'}"

        # 更新对应的全局状态
        if progress_status == volatility_refresh_status:
            volatility_refresh_status.update(updated_progress)
        elif progress_status == stock_refresh_status:
            stock_refresh_status.update(updated_progress)
        elif progress_status == new_stock_analysis_status:
            new_stock_analysis_status.update(updated_progress)

        return [updated_progress]

    except Exception as e:
        logger.error(f"监控多进程任务进度失败: {str(e)}")
        return [dash.no_update]



@app.callback(
    [
        Output({"type": "plot-container", "symbol": MATCH, "plot_type": MATCH}, "children"),
        Output({"type": "plot-progress", "symbol": MATCH, "plot_type": MATCH}, "style"),
        Output({"type": "plot-progress", "symbol": MATCH, "plot_type": MATCH}, "value"),
        Output({"type": "plot-progress", "symbol": MATCH, "plot_type": MATCH}, "label"),
    ],
    [
        Input({"type": "generate-plot", "symbol": MATCH, "plot_type": MATCH}, "n_clicks"),
        Input("progress-interval", "n_intervals")
    ],
    [
        State({"type": "plot-progress", "symbol": MATCH, "plot_type": MATCH}, "style"),
        State({"type": "plot-progress", "symbol": MATCH, "plot_type": MATCH}, "value")
    ]
)
def handle_plot_actions(gen_clicks, n_intervals, current_style, current_value):
    logger.info("点击了生成图表按钮")
    ctx = callback_context
    
    if not ctx.triggered:
        raise PreventUpdate
    
    trigger_id = ctx.triggered[0]['prop_id'].rsplit('.', 1)[0]

    logger.info(f"ctx.triggered: {ctx.triggered}")
    logger.info(f"trigger_id: {trigger_id}")
    # 处理定时器触发的进度更新
    if trigger_id == "progress-interval":
        # 如果进度条已经隐藏或完成，停止更新
        if current_style and current_style.get("visibility") == "hidden":
            raise PreventUpdate
        if current_value is None or current_value >= 100:
            raise PreventUpdate
        new_value = min(current_value + 25, 100)
        return dash.no_update, dash.no_update, new_value, f"{new_value}%"
    
    # 处理生成图表按钮
    if '"type":"generate-plot"' in trigger_id:
        pattern = json.loads(trigger_id)
        stock_symbol = pattern['symbol']
        plot_type = pattern['plot_type']

        if gen_clicks:
            # 防止重复点击：检查是否已有相同的图表生成任务在运行
            global chart_generation_status
            task_key = f"{stock_symbol}_{plot_type}"
            if chart_generation_status.get(task_key, {}).get("progress", 100) < 100:
                logger.info(f"图表生成任务 {task_key} 已在运行中，忽略重复点击")
                return dash.no_update, dash.no_update, dash.no_update, dash.no_update
            freq_list = ['W', '2W', 'ME', '2M']
            
            # 显示进度条
            progress_style = {"visibility": "visible", "margin-top": "10px"}
            
            # 获取当前股票数据
            stock_data = db_ops.get_stock_data(stock_symbol)
            if stock_data and 'plots' in stock_data:
                # 删除旧的同类型图表
                plots_to_keep = {}
                for key, value in stock_data['plots'].items():
                    if (plot_type == 'vol' and not key.startswith('volatility_plot_')) or \
                        (plot_type == 'ret' and not key.startswith('return_plot_')):
                        plots_to_keep[key] = value
                stock_data['plots'] = plots_to_keep
                # 更新数据库
                db_ops.save_stock_data(stock_symbol, stock_data)
            
            try:
                logger.info("开始生成并保存新图表")

                # 设置图表生成状态
                chart_generation_status[task_key] = {"progress": 10, "message": f"正在生成{plot_type}图表..."}

                # 直接调用已优化的图表生成函数（已有装饰器优化）
                plot_info = db_ops.generate_and_save_plots(stock_symbol, plot_type, freq_list)

                if not plot_info:
                    # 清理图表生成状态
                    chart_generation_status[task_key] = {"progress": 100, "message": "图表生成失败"}

                    # 如果无法生成图表，显示错误信息而不是抛出异常
                    error_message = html.Div([
                        html.P(f"无法为股票 {stock_symbol} 生成 {plot_type} 类型的图表", className="text-danger"),
                        html.P("可能原因：", className="mt-2"),
                        html.Ul([
                            html.Li("没有足够的历史数据"),
                            html.Li("数据格式不兼容"),
                            html.Li("新增股票需要完整刷新数据后才能生成图表")
                        ])
                    ])
                    return error_message, {"visibility": "hidden"}, 100, "失败"

                # 获取并显示更新后的图表
                def get_stock_data_task():
                    return db_ops.get_stock_data(stock_symbol)

                # 使用多线程处理IO密集型的数据库查询
                optimizer = get_task_optimizer()
                future_data = optimizer.optimize_task("database_operations", get_stock_data_task)
                stock_data = future_data.result()

                plots = component_renderers.render_saved_plots(stock_data, plot_type)  # 只返回指定类型的图表

                # 清理图表生成状态
                chart_generation_status[task_key] = {"progress": 100, "message": "图表生成完成"}

                return plots, {"visibility": "hidden"}, 100, "完成"

            except Exception as e:
                logger.error(f"生成图表时出错: {str(e)}")
                logger.error(f"{traceback.format_exc()}")

                # 清理图表生成状态
                chart_generation_status[task_key] = {"progress": 100, "message": f"图表生成失败: {str(e)}"}

                error_message = html.Div([
                    html.P(f"生成图表时出错: {str(e)}", className="text-danger"),
                    html.P("可能原因：", className="mt-2"),
                    html.Ul([
                        html.Li("数据格式不兼容"),
                        html.Li("新增股票需要完整刷新数据后才能生成图表")
                    ])
                ])
                return error_message, {"visibility": "hidden"}, 100, "失败"
        
    raise PreventUpdate

# 添加一个新的回调来控制定时器
@app.callback(
    Output("progress-interval", "disabled"),
    [Input({"type": "plot-progress", "symbol": ALL, "plot_type": ALL}, "value")]
)
def control_interval(values):
    # 如果所有进度值都是 None、0 或 100，则禁用定时器
    if not values or all(v is None or v == 0 or v >= 100 for v in values):
        return True
    return False

# 添加图片放大的回调函数
@app.callback(
    [
        Output("image-modal", "is_open"),
        Output("enlarged-image", "src"),
        Output("image-modal-title", "children")
    ],
    [
        Input({"type": "plot-image", "key": ALL}, "n_clicks"),
        Input("close-image-modal", "n_clicks")
    ],
    [
        State("image-modal", "is_open"),
        State({"type": "plot-image", "key": ALL}, "src"),
        State({"type": "plot-image", "key": ALL}, "id")
    ]
)
def toggle_image_modal(image_clicks, close_clicks, is_open, srcs, ids):
    ctx = dash.callback_context
    
    # 如果没有触发回调或关闭按钮被点击
    if not ctx.triggered or (ctx.triggered[0]['prop_id'] == 'close-image-modal.n_clicks' and close_clicks):
        return False, dash.no_update, dash.no_update
    
    # 如果模态框已经打开并且点击的不是关闭按钮
    if is_open and ctx.triggered[0]['prop_id'] != 'close-image-modal.n_clicks':
        return False, dash.no_update, dash.no_update
    
    # 处理图片点击
    if ctx.triggered[0]['prop_id'] != 'close-image-modal.n_clicks':
        try:
            # 获取触发组件的属性ID
            button_id = ctx.triggered[0]['prop_id'].rsplit('.', 1)[0]
            logger.debug(f"触发的按钮ID: {button_id}")
            
            # 检查是否是有效的JSON格式
            if not button_id.startswith('{'):
                logger.debug(f"无效的按钮ID格式: {button_id}")
                return is_open, dash.no_update, dash.no_update
            
            # 解析触发器的 ID
            parsed_id = json.loads(button_id)
            
            # 找到被点击图片的索引和对应图片
            clicked_index = None
            for i, (clicks, img_id) in enumerate(zip(image_clicks, ids)):
                if clicks is not None and clicks > 0:
                    logger.debug(f"检查图片: {i}, ID: {img_id}")
                    if img_id['type'] == 'plot-image' and img_id['key'] == parsed_id['key']:
                        clicked_index = i
                        break
            
            if clicked_index is not None and clicked_index < len(srcs):
                title = f"图片详情 - {parsed_id['key']}"
                return True, srcs[clicked_index], title
            else:
                logger.debug(f"未找到匹配的图片，点击状态: {image_clicks}")
                return is_open, dash.no_update, dash.no_update
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            logger.error(f"问题字符串: '{button_id}'")
            return is_open, dash.no_update, dash.no_update
        except Exception as e:
            logger.error(f"处理图片点击时出错: {e}")
            import traceback
            traceback.print_exc()
            return is_open, dash.no_update, dash.no_update
    
    # 默认返回，保持当前状态
    return is_open, dash.no_update, dash.no_update

# 添加CSS样式
app.index_string = '''
<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>{%title%}</title>
        {%favicon%}
        {%css%}
        <!-- 直接添加脚本在DOM加载后修复模态框宽度 -->
        <script>
            (function() {
                // 等待DOM完全加载
                document.addEventListener('DOMContentLoaded', function() {
                    // 监听模态框打开
                    document.addEventListener('click', function(e) {
                        if (e.target.closest('[id*="show-modal-button"]')) {
                            setTimeout(resizeModal, 100);
                            // 多次尝试以确保样式生效
                            setTimeout(resizeModal, 300);
                            setTimeout(resizeModal, 600);
                        }
                    });
                    
                    function resizeModal() {
                        // 获取模态框元素
                        var modal = document.getElementById('full-table-modal');
                        if (modal) {
                            // 设置模态框样式
                            modal.style.maxWidth = '100%';
                            modal.style.width = '100%';
                            modal.style.paddingRight = '0';
                            
                            // 设置对话框样式
                            var dialog = modal.querySelector('.modal-dialog');
                            if (dialog) {
                                dialog.style.maxWidth = '95%';
                                dialog.style.width = '95%';
                                dialog.style.margin = '30px auto';
                                dialog.style.transform = 'none';
                            }
                            
                            // 设置内容样式
                            var content = modal.querySelector('.modal-content');
                            if (content) {
                                content.style.width = '100%';
                                content.style.maxWidth = '100%';
                            }
                            
                            // 设置主体样式
                            var body = modal.querySelector('.modal-body');
                            if (body) {
                                body.style.overflowX = 'auto';
                                body.style.padding = '15px';
                            }
                        }
                    }
                    
                    // 监听Bootstrap模态框事件
                    document.addEventListener('shown.bs.modal', function(e) {
                        if (e.target.id === 'full-table-modal') {
                            resizeModal();
                        }
                    });
                });
            })();
        </script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
        <!-- 直接添加特定于期权模态框的内联样式覆盖Bootstrap -->
        <style>
            /* 特殊处理：强制覆盖期权表格模态框的宽度 */
            body #full-table-modal.modal.fade.show {
                padding-right: 0 !important;
                max-width: 100% !important;
                width: 100% !important;
                overflow-x: auto !important;
            }
            
            body #full-table-modal.modal .modal-dialog,
            body div#full-table-modal .modal-dialog {
                max-width: 95% !important;
                width: 95% !important;
                margin: 30px auto !important;
                transform: none !important;
            }
            
            #full-table-modal .modal-content {
                width: 100% !important;
                max-width: 100% !important;
            }
            
            /* 修复滚动问题 */
            #full-table-modal .modal-body {
                overflow-x: auto !important;
                padding: 15px !important;
            }
        </style>
        <style>
            .stock-preview-list {
                margin-bottom: 10px;
            }
            .stock-preview-list .stock-item {
                padding: 8px;
                border-bottom: 1px solid #444;
            }
            .full-stock-list {
                max-height: 300px;
                overflow-y: auto;
                border-top: 1px solid #444;
                margin-top: 10px;
                padding-top: 10px;
            }
            .stock-item:last-child {
                border-bottom: none;
            }
            .stock-link {
                cursor: pointer;
                color: #fff;
                text-decoration: none;
            }
            .stock-link:hover {
                color: #007bff;
                text-decoration: none;
            }
            .card {
                background-color: #2a2a2a;
                border: 1px solid #444;
            }
            .card-header {
                background-color: #1a1a1a;
                border-bottom: 1px solid #444;
            }
            .modal-content {
                background-color: #2a2a2a;
                color: #fff;
            }
            .modal-header {
                border-bottom: 1px solid #444;
            }
            .modal-footer {
                border-top: 1px solid #444;
            }
            .table {
                color: #fff;
            }
            .plot-container {
                height: 500px;
                overflow-y: auto;
                padding: 10px;
                background-color: #1a1a1a;
                border: 1px solid #444;
                border-radius: 4px;
            }
            
            .plot-image:hover {
                opacity: 0.8;
                transition: opacity 0.2s ease-in-out;
            }
            
            /* 修改图表容器样式 */
            .chart-container {
                width: 100%;          /* 容器宽度固定为100% */
                height: 250px;        /* 容器高度固定 */
                position: relative;   /* 建立新的定位上下文 */
                overflow: hidden;     /* 防止内容溢出 */
            }
            
            /* 图表样式 */
            .chart-content {
                position: absolute;  /* 绝对定位 */
                top: 0;
                left: 0;
                width: 100%;        /* 适应容器宽度 */
                height: 100%;       /* 适应容器高度 */
            }
            
            /* 更新模态框样式 */
            .modal {
                position: fixed !important;
            }
            
            .modal-dialog {
                position: fixed !important;
                margin: 0;
                padding: 0;
                max-width: 90vw;
                width: auto;
                transition: none !important;
            }
            
            .modal-content {
                max-height: 90vh !important; /* 设置为视窗高度的90% */
                overflow-y: auto;
            }
            
            /* 添加滚动条样式 */
            .modal-content::-webkit-scrollbar {
                width: 8px;
            }
            
            .modal-content::-webkit-scrollbar-track {
                background: #1a1a1a;
            }
            
            .modal-content::-webkit-scrollbar-thumb {
                background: #888;
                border-radius: 4px;
            }
            
            .modal-content::-webkit-scrollbar-thumb:hover {
                background: #555;
            }
            
            .modal-at-click .modal-dialog {
                position: fixed;
                margin: 0;
                transform: none;
            }
            
            .modal-content {
                max-height: 90vh !important; /* 设置为视窗高度的90% */
                overflow-y: auto;
            }
            
            /* 添加到现有的CSS样式中 */
            .modal-dialog {
                margin: 1.75rem auto;
            }
            
            /* 特别为期权表格模态框设置更大的宽度 - 使用重要标记和多重选择器 */
            body #full-table-modal.modal .modal-dialog,
            .full-width-modal.modal .modal-dialog,
            div#full-table-modal div.modal-dialog,
            #full-table-modal .modal-dialog {
                max-width: 95% !important;
                width: 95% !important;
                margin-left: auto !important;
                margin-right: auto !important;
            }
            
            /* 强制覆盖模态框本身的样式 */
            #full-table-modal.modal.fade.show {
                padding-right: 0 !important;
                max-width: 100% !important;
            }

            .modal-content {
                height: 90vh;
            }

            .modal-body {
                padding: 0;
                height: calc(90vh - 120px);  /* 减去header和footer的高度 */
                overflow: hidden;
            }

            /* 自定义滚动条样式 */
            ::-webkit-scrollbar {
                width: 10px;
                height: 10px;
            }

            ::-webkit-scrollbar-track {
                background: #1a1a1a;
            }

            ::-webkit-scrollbar-thumb {
                background: #888;
                border-radius: 5px;
            }

            ::-webkit-scrollbar-thumb:hover {
                background: #555;
            }

            /* 告警详情模态框专用滚动条样式 */
            #alert-detail-modal-body::-webkit-scrollbar {
                width: 12px;
            }

            #alert-detail-modal-body::-webkit-scrollbar-track {
                background: #2a2a2a;
                border-radius: 6px;
            }

            #alert-detail-modal-body::-webkit-scrollbar-thumb {
                background: linear-gradient(180deg, #6c757d 0%, #495057 100%);
                border-radius: 6px;
                border: 2px solid #2a2a2a;
            }

            #alert-detail-modal-body::-webkit-scrollbar-thumb:hover {
                background: linear-gradient(180deg, #495057 0%, #343a40 100%);
            }

            #alert-detail-modal-body::-webkit-scrollbar-thumb:active {
                background: linear-gradient(180deg, #343a40 0%, #212529 100%);
            }

            /* 为告警详情模态框添加平滑滚动 */
            #alert-detail-modal-body {
                scroll-behavior: smooth;
            }

            /* 告警详情模态框专用样式 - 优化布局和尺寸 */
            #alert-detail-modal .modal-dialog {
                max-width: 98vw !important;
                width: 98vw !important;
                margin: 10px auto !important;  /* 减少左右边距以获得更多空间 */
            }

            #alert-detail-modal .modal-content {
                max-height: 90vh !important;
                display: flex !important;
                flex-direction: column !important;
            }

            #alert-detail-modal .modal-body {
                flex: 1 !important;
                overflow: hidden !important;
                padding: 0 !important;
            }

            /* 优化告警详情模态框内卡片的显示 */
            #alert-detail-modal-body .card {
                margin-bottom: 15px !important;
                border: 1px solid #dee2e6 !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
            }

            #alert-detail-modal-body .card-header {
                background-color: #f8f9fa !important;
                border-bottom: 1px solid #dee2e6 !important;
                font-weight: bold !important;
            }

            #alert-detail-modal-body .card-body {
                padding: 10px !important;
            }

            /* 优化告警详情模态框的卡片间距和文字大小 */
            #alert-detail-modal-body .card {
                font-size: 14px !important;
            }

            #alert-detail-modal-body .card-header h5 {
                font-size: 16px !important;
                margin-bottom: 0 !important;
            }

            #alert-detail-modal-body h6 {
                font-size: 14px !important;
                margin-bottom: 8px !important;
            }

            #alert-detail-modal-body p, 
            #alert-detail-modal-body span {
                font-size: 13px !important;
                line-height: 1.3 !important;
            }

            /* 确保模态框能够根据内容自适应高度 */
            #alert-detail-modal .modal-content {
                min-height: auto !important;
                height: auto !important;
            }

            #alert-detail-modal-body {
                max-height: calc(90vh - 120px) !important; /* 90vh减去头部和底部的高度 */
                height: auto !important;
            }

            /* 表格样式优化 */
            .dash-table-container {
                height: 100%;
                width: 100%;
                max-width: none !important;
            }

            .dash-spreadsheet-container {
                max-height: none !important;
                height: 100%;
            }

            .dash-spreadsheet-inner {
                height: 100%;
                overflow: auto !important;
            }

            /* 添加到现有的 CSS 样式中 */
.dash-spreadsheet td.cell--fixed-left,
.dash-spreadsheet th.column-header--fixed-left {
    position: sticky !important;
    left: 0 !important;
    z-index: 999 !important;
    box-shadow: 2px 0px 3px rgba(0,0,0,0.2) !important;
}

.dash-spreadsheet td.cell--fixed-left {
    background-color: rgb(50, 50, 50) !important;
}

.dash-spreadsheet th.column-header--fixed-left {
    background-color: rgb(30, 30, 30) !important;
}

/* 确保表头在滚动时始终固定 */
.dash-spreadsheet .dash-header {
    position: sticky !important;
    top: 0 !important;
    z-index: 998 !important;
}

/* 修复表格宽度计算问题 */
.dash-spreadsheet {
    width: 100% !important;
    max-width: none !important;
}

/* 确保单元格边框连续 */
.dash-spreadsheet td, .dash-spreadsheet th {
    box-sizing: border-box !important;
    border: 1px solid #444 !important;
}

            /* 添加阴影效果以区分固定列 */
            .dash-spreadsheet td.cell--fixed-left::after,
            .dash-spreadsheet th.column-header--fixed-left::after {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                width: 2px;
                background: linear-gradient(to right, rgba(0,0,0,0.2), rgba(0,0,0,0));
            }
            
            /* 自定义模态框样式 */
            .image-modal .modal-dialog {
                max-width: 90%;  /* 增加到页面宽度的90% */
                width: 90%;
                margin: 30px auto;
            }
            
            .image-modal .modal-body {
                padding: 0;
                overflow: auto;  /* 允许内容滚动 */
                max-height: 90vh;  /* 视口高度的80% */
            }
            
            #enlarged-image {
                width: 100%;
                height: auto;
                object-fit: contain;  /* 保持图片比例 */
            }
        </style>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                let clickPosition = { x: 0, y: 0 };
                
                // 记录点击位置
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.stock-link')) {
                        clickPosition.x = e.clientX;
                        clickPosition.y = e.clientY;
                    }
                    
                    // 检查是否是期权数据表格按钮
                    if (e.target.closest('[id*="show-modal-button"]')) {
                        // 设置一个定时器来确保模态框已被渲染
                        setTimeout(function() {
                            const fullTableModal = document.getElementById('full-table-modal');
                            if (fullTableModal) {
                                const dialog = fullTableModal.querySelector('.modal-dialog');
                                if (dialog) {
                                    dialog.style.maxWidth = '95vw';
                                    dialog.style.width = '95vw'; 
                                    dialog.style.margin = '30px auto';
                                }
                            }
                        }, 100);
                    }
                });
                
                // 监听模态框的显示
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        const modal = mutation.target;
                        
                        // 处理股票详情模态框位置
                        if (modal.classList.contains('show') && modal.classList.contains('modal-at-click')) {
                            const dialog = modal.querySelector('.modal-dialog');
                            if (dialog) {
                                const viewportHeight = window.innerHeight;
                                const dialogHeight = dialog.offsetHeight;
                                
                                // 计算理想的垂直位置
                                let top = clickPosition.y;
                                
                                // 如果点击位置在下半部分，向上移动对话框
                                if (clickPosition.y > viewportHeight / 2) {
                                    top = clickPosition.y - dialogHeight/2;
                                }
                                
                                // 确保对话框不会超出视窗
                                top = Math.max(20, Math.min(top, viewportHeight - dialogHeight - 20));
                                
                                // 设置位置
                                dialog.style.top = top + 'px';
                                dialog.style.left = '50%';
                                dialog.style.transform = 'translateX(-50%)';
                            }
                        }
                        
                        // 处理期权数据表格模态框大小
                        if (modal.id === 'full-table-modal' && modal.classList.contains('show')) {
                            const dialog = modal.querySelector('.modal-dialog');
                            if (dialog) {
                                dialog.style.maxWidth = '95vw';
                                dialog.style.width = '95vw';
                                dialog.style.margin = '30px auto';
                            }
                        }
                    });
                });
                
                // 观察模态框的类变化
                const stockModal = document.getElementById('stock-detail-modal');
                if (stockModal) {
                    observer.observe(stockModal, { 
                        attributes: true, 
                        attributeFilter: ['class']
                    });
                }
                
                // 观察期权表格模态框
                const optionModal = document.getElementById('full-table-modal');
                if (optionModal) {
                    observer.observe(optionModal, { 
                        attributes: true, 
                        attributeFilter: ['class']
                    });
                }
            });
        </script>
    </head>
    <body>
        {%app_entry%}
        <footer>
            {%config%}
            {%scripts%}
            {%renderer%}
        </footer>
    </body>
</html>
'''

# 添加界面切换的回调函数
@app.callback(
    [
        Output("stock-analysis-content", "style"),
        Output("option-analysis-content", "style"),
        Output("strategy-center-content", "style"),
        Output("machine-analysis-content", "style"),
        Output("line-chart-content", "style"),
        Output("current-strategy-card", "style"),
        Output("overview-title", "children"),
        Output("overview-info", "children")
    ],
    [
        Input("stock-analysis-btn", "n_clicks"),
        Input("option-analysis-btn", "n_clicks"),
        Input("strategy-center-btn", "n_clicks"),
        Input("machine-analysis-btn", "n_clicks"),
        Input("line-chart-btn", "n_clicks")
    ]
)
def switch_interface(stock_clicks, option_clicks, strategy_clicks, machine_clicks, line_chart_clicks):
    ctx = callback_context
    if not ctx.triggered:
        # 如果没有触发，默认显示股票分析
        return ({"display": "block"}, {"display": "none"}, {"display": "none"}, {"display": "none"}, {"display": "none"},
                {"display": "block"}, "股票分析概览", "选择股票以查看详细分析")

    button_id = ctx.triggered[0]["prop_id"].split(".")[0]
    # 获取所有股票信息
    stock_info = list(db_ops.get_all_stocks().values())

    if button_id == "stock-analysis-btn":
        return ({"display": "block"}, {"display": "none"}, {"display": "none"}, {"display": "none"}, {"display": "none"},
                {"display": "block"}, "股票分析概览",
                f"已分析股票数: {len(stock_info)} | 最近分析日期: {max([s['analysis_date'] for s in stock_info]) if stock_info else '无数据'}")
    elif button_id == "option-analysis-btn":
        return ({"display": "none"}, {"display": "block"}, {"display": "none"}, {"display": "none"}, {"display": "none"},
                {"display": "block"}, "期权分析概览",
                "选择指数类型和日期以查看期权数据，加载后将显示最新更新时间")
    elif button_id == "strategy-center-btn":
        return ({"display": "none"}, {"display": "none"}, {"display": "block"}, {"display": "none"}, {"display": "none"},
                {"display": "none"}, "策略中心",
                "波动率告警监控和策略信号分析")
    elif button_id == "machine-analysis-btn":
        return ({"display": "none"}, {"display": "none"}, {"display": "none"}, {"display": "block"}, {"display": "none"},
                {"display": "none"}, "机器分析",
                "基于SARIMAX和GARCH模型的时间序列自相关告警分析")
    elif button_id == "line-chart-btn":
        return ({"display": "none"}, {"display": "none"}, {"display": "none"}, {"display": "none"}, {"display": "block"},
                {"display": "none"}, "技术指标折线图",
                "输入股票代码和指数代码，选择技术指标列名生成折线图")

# 修改期权数据加载的回调函数
@app.callback(
    [
        Output("option-data-display", "children"),
        Output("option-data-store", "data"),
        Output("full-option-table", "data"),
        Output("full-option-table", "columns"),
        Output("show-full-table-container", "children")
    ],
    [Input("load-option-data", "n_clicks")],
    [
        State("index-type-dropdown", "value"),
        State("date-picker", "date")
    ]
)
def load_option_data(n_clicks, index_type, selected_date):
    if not n_clicks:
        return [], {}, [], [], None
    
    try:
        # 将日期字符串转换为datetime对象
        date_obj = datetime.strptime(selected_date.split('T')[0], '%Y-%m-%d')
        date_str = date_obj.strftime('%Y%m%d')
        
        # 使用任务优化器处理IO密集型的期权数据加载
        optimizer = get_task_optimizer()

        def load_option_data_task():
            """使用任务优化器加载期权数据"""
            optimizer = get_task_optimizer()

            # 并发加载期权相关数据
            df_future = optimizer.optimize_task("option_data_loading", db_ops.get_option_data, index_type, date_str)
            detail_future = optimizer.optimize_task("database_operations", db_ops.get_option_detail_data, date_str, index_type)
            time_future = optimizer.optimize_task("database_operations", db_ops.get_option_update_time, index_type, date_str)

            # 获取结果
            df = df_future.result()
            detail_data = detail_future.result()
            update_time = time_future.result()

            return df, detail_data, update_time

        # 异步加载期权数据
        future = optimizer.optimize_task("option_data_loading", load_option_data_task)
        df, detail_data, update_time = future.result()
        
        if df.empty:
            return html.Div([
                dbc.Alert("未找到该日期的期权数据", color="warning")
            ]), {}, [], [], None

        # 获取当日收盘价
        today_close = detail_data['today_close'] if detail_data else None
        if detail_data.get('filtered_option'):
            df_filtered = db_ops.get_option_filtered_data(detail_data['filtered_option'])
            # 根据指数类型设置价格范围
            if index_type == '50':
                try_price_low = round(today_close * 0.92, 2)
                try_price_high = round(today_close * 1.08, 2)
            elif index_type == '300':
                try_price_low = round(today_close * 0.91, 2)
                try_price_high = round(today_close * 1.09, 2)
            else:  # 中证1000
                try_price_low = round(today_close * 0.9, 2)
                try_price_high = round(today_close * 1.1, 2)
        else:
            df_filtered = df
            today_close = None
            try_price_low = None
            try_price_high = None
        
        # 创建统计信息卡片
        stats_cards = dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H6("期权总数"),
                        html.H3(f"{len(df)}")
                    ])
                ], className="mb-3")
            ], width=3),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H6("筛选范围内期权数"),
                        html.H3(f"{len(df_filtered)}")
                    ])
                ], className="mb-3")
            ], width=3),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H6("价格区间"),
                        html.H3([
                            html.Span(f"{try_price_low:.1f}", style={"fontSize": "1.5rem"}),
                            html.Span(" - ", style={"fontSize": "1.5rem"}),
                            html.Span(f"{try_price_high:.1f}", style={"fontSize": "1.5rem"})
                        ])
                    ])
                ], className="mb-3")
            ], width=3),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H6("当前价格"),
                        html.H3(f"{today_close:.2f}")
                    ])
                ], className="mb-3")
            ], width=3)
        ])
        
        # 创建更新时间卡片
        update_time_card = dbc.Row([
            dbc.Col([
                html.Div([
                    html.Small([
                        html.I(className="fas fa-clock mr-1", style={"marginRight": "3px"}),  # 添加时钟图标
                        "数据更新时间: ",
                        html.Span(f"{update_time if update_time else '无更新时间信息'}", 
                                style={"color": "#fd7e14", "fontWeight": "500"})
                    ], style={"fontSize": "0.75rem", "color": "#6c757d"})
                ], className="text-right mb-2")
            ], width=12)
        ])
        
        # 创建预览表格
        preview_table = dash_table.DataTable(
            data=df_filtered.reset_index().head(20).to_dict('records'),
            columns=[{"name": i, "id": i} for i in df_filtered.reset_index().columns],
            style_table={'overflowX': 'auto'},
            style_cell={
                'backgroundColor': 'rgb(50, 50, 50)',
                'color': 'white',
                'textAlign': 'left'
            },
            style_header={
                'backgroundColor': 'rgb(30, 30, 30)',
                'fontWeight': 'bold'
            },
            page_size=20
        )
        
        # 准备完整表格的数据
        full_table_data = df.reset_index().to_dict('records')
        full_table_columns = [{"name": i, "id": i} for i in df.reset_index().columns]
        
        # 创建显示按钮
        show_full_table_button = html.Div([
            dbc.Button("查看完整数据表格", 
                      id={"type": "show-modal-button", "index": 0}, 
                      className="mb-3",
                      style={"fontSize": "1rem", "padding": "0.5rem 1rem"})
        ])
        
        # 组装UI
        main_content = html.Div([
            # 在统计卡片上方添加更新时间信息
            update_time_card,
            stats_cards,
            html.Hr(),
            show_full_table_button,
            html.Div([
                html.H6(f"期权数据预览（价格区间：{try_price_low:.2f} - {try_price_high:.2f}）"),
                preview_table
            ])
        ])
        
        return main_content, {"data": full_table_data}, full_table_data, full_table_columns, show_full_table_button

    except Exception as e:
        logger.error(f"加载期权数据时出错: {str(e)}")
        return html.Div([
            dbc.Alert(f"加载数据时出错: {str(e)}", color="danger")
        ]), {}, [], [], None

# 修改模态框的回调，使用模式匹配回调
@app.callback(
    Output("full-table-modal", "is_open"),
    [
        Input({"type": "show-modal-button", "index": ALL}, "n_clicks"),
        Input("close-full-table-modal", "n_clicks")
    ],
    [State("full-table-modal", "is_open")]
)
def toggle_modal(show_clicks, close_clicks, is_open):
    ctx = callback_context
    if not ctx.triggered:
        return is_open
    
    trigger_id = ctx.triggered[0]["prop_id"].split(".")[0]
    
    if trigger_id == "close-full-table-modal":
        return False
    
    if any(click for click in show_clicks if click):
        return True
        
    return is_open

@app.callback(
    [
        Output("full-option-table", "style_table"),
        Output("full-option-table", "fixed_rows"),
        Output("full-option-table", "fixed_columns")
    ],
    [Input("full-table-modal", "is_open")]
)
def update_table_style(is_open):
    if is_open:
        return {
            'height': 'calc(100vh - 200px)',  # 动态高度
            'width': '100%',
            'overflowY': 'auto',
            'overflowX': 'auto',
            'minWidth': '100%'
        }, {'headers': True}, {'headers': True, 'data': 1}
    return {'display': 'none'}, {'headers': True}, {'headers': True, 'data': 0}

# 新增回调函数，确保第一列始终固定
@app.callback(
    Output("full-option-table", "style_cell_conditional"),
    [Input("full-option-table", "columns")]
)
def update_table_cell_style(columns):
    if not columns or len(columns) == 0:
        return []
    
    # 获取第一列的ID
    first_column_id = columns[0]['id']
    
    return [
        {
            'if': {'column_id': first_column_id},
            'position': 'sticky',
            'left': 0,
            'backgroundColor': 'rgb(50, 50, 50)',
            'zIndex': 999
        }
    ]

# 添加展开/收起告警历史表格的回调函数
@app.callback(
    [
        Output({"type": "alert-table-container", "alert_type": MATCH, "stock_id": MATCH}, "children"),
        Output({"type": "expand-alert-button", "alert_type": MATCH, "stock_id": MATCH}, "children")
    ],
    [Input({"type": "expand-alert-button", "alert_type": MATCH, "stock_id": MATCH}, "n_clicks")],
    [
        State({"type": "alert-full-data", "alert_type": MATCH, "stock_id": MATCH}, "data"),
        State({"type": "expand-alert-button", "alert_type": MATCH, "stock_id": MATCH}, "children")
    ]
)
def toggle_alert_table(n_clicks, full_data, current_text):
    if not n_clicks:
        raise PreventUpdate
        
    # 获取匹配ID信息
    ctx = callback_context
    trigger_id = ctx.triggered[0]["prop_id"].split(".")[0]
    button_data = json.loads(trigger_id)
    alert_type = button_data["alert_type"]
    
    # 如果当前按钮显示的是"显示全部"，则展开表格显示所有记录
    if current_text == "显示全部":
        # 为不同类型的告警准备不同的表格列
        if alert_type.startswith("price_cross_"):
            ma_name = alert_type.replace("price_cross_", "")
            table = dbc.Table([
                html.Thead(html.Tr([
                    html.Th("日期"),
                    html.Th("类型"),
                    html.Th("价格"),
                    html.Th(f"{ma_name}值"),
                    html.Th(f"boll差值比"),
                    html.Th(f"boll百分比"),
                    html.Th(f"20-60差值"),
                    html.Th(f"20-60百分比")
                ])),
                html.Tbody([
                    html.Tr([
                        html.Td(alert['date']),
                        html.Td(html.Span("向上", className="text-success") if alert['type'] == 'up' else html.Span("向下", className="text-danger")),
                        html.Td(f"{alert['price']:.2f}"),
                        html.Td(f"{alert['ma_value']:.2f}"),
                        html.Td(f"{alert['boll_wide']:.2f}"),
                        html.Td(f"{alert['boll_wide_percent'] * 100:.2f}%"),
                        html.Td(f"{alert['ma20-ma60']:.2f}"),
                        html.Td(f"{alert['20-60_percent'] * 100:.2f}%")
                    ]) for alert in full_data  # 显示全部数据
                ])
            ], bordered=True, hover=True, size="sm")
        else:  # bullish或bearish
            table = dbc.Table([
                html.Thead(html.Tr([
                    html.Th("日期"),
                    html.Th("价格"),
                    html.Th("MA5"),
                    html.Th("MA10"),
                    html.Th("MA20")
                ])),
                html.Tbody([
                    html.Tr([
                        html.Td(alert['date']),
                        html.Td(f"{alert['price']:.2f}"),
                        html.Td(f"{alert['ma5']:.2f}"),
                        html.Td(f"{alert['ma10']:.2f}"),
                        html.Td(f"{alert['ma20']:.2f}")
                    ]) for alert in full_data  # 显示全部数据
                ])
            ], bordered=True, hover=True, size="sm")
        
        return table, "收起"
    else:
        # 如果当前按钮显示的是"收起"，则收起表格只显示最近5条记录
        if alert_type.startswith("price_cross_"):
            ma_name = alert_type.replace("price_cross_", "")
            table = dbc.Table([
                html.Thead(html.Tr([
                    html.Th("日期"),
                    html.Th("类型"),
                    html.Th("价格"),
                    html.Th(f"{ma_name}值"),
                    html.Th(f"5-60差值比"),
                    html.Th(f"5-60百分比"),
                    html.Th(f"20-60差值比"),
                    html.Th(f"20-60百分比")
                ])),
                html.Tbody([
                    html.Tr([
                        html.Td(alert['date']),
                        html.Td(html.Span("向上", className="text-success") if alert['type'] == 'up' else html.Span("向下", className="text-danger")),
                        html.Td(f"{alert['price']:.2f}"),
                        html.Td(f"{alert['ma_value']:.2f}"),
                        html.Td(f"{alert['ma5-ma60']:.2f}"),
                        html.Td(f"{alert['5-60_percent'] * 100:.2f}%"),
                        html.Td(f"{alert['ma20-ma60']:.2f}"),
                        html.Td(f"{alert['20-60_percent'] * 100:.2f}%")
                    ]) for alert in full_data[-5:]  # 只显示最近5条
                ])
            ], bordered=True, hover=True, size="sm")
        else:  # bullish或bearish
            table = dbc.Table([
                html.Thead(html.Tr([
                    html.Th("日期"),
                    html.Th("价格"),
                    html.Th("MA5"),
                    html.Th("MA10"),
                    html.Th("MA20")
                ])),
                html.Tbody([
                    html.Tr([
                        html.Td(alert['date']),
                        html.Td(f"{alert['price']:.2f}"),
                        html.Td(f"{alert['ma5']:.2f}"),
                        html.Td(f"{alert['ma10']:.2f}"),
                        html.Td(f"{alert['ma20']:.2f}")
                    ]) for alert in full_data[-5:]  # 只显示最近5条
                ])
            ], bordered=True, hover=True, size="sm")
        
        return table, "显示全部"

@app.callback(
    [
        Output("full-table-modal", "className"),
        Output("full-table-modal", "style")
    ],
    [Input("full-table-modal", "is_open")]
)
def set_modal_class(is_open):
    if is_open:
        return "resizable-modal full-width-modal", {"maxWidth": "95%", "width": "95%", "display": "block", "paddingRight": "0"}
    return "full-width-modal", {"maxWidth": "95%", "width": "95%"}

@app.callback(
    [
        Output("refresh-progress-bar", "value"),
        Output("refresh-progress-message", "children"),
        Output("stocks-data-store", "data", allow_duplicate=True),  # 添加这个输出
    ],
    [Input("refresh-progress-interval", "n_intervals")],
    [State("refresh-progress-store", "data")],
    prevent_initial_call=True
)
def update_progress_display(n_intervals, progress_data):
    # 确定要使用的全局变量
    global stock_refresh_status, new_stock_analysis_status, volatility_refresh_status
    
    # 检查progress_data中的类型，决定使用哪个全局变量
    data_type = progress_data.get("type", "") if progress_data else ""
    source_type = progress_data.get("source", "refresh") if progress_data else "refresh"
    
    # 根据来源选择正确的全局变量
    if source_type == "new_stock":
        progress_status = new_stock_analysis_status
        logger.debug("使用新股票分析状态")
    elif source_type == "volatility_refresh":
        progress_status = volatility_refresh_status
        logger.debug("使用波动率刷新状态")
    else:
        progress_status = stock_refresh_status
        logger.debug("使用刷新数据状态")
    
    if not progress_status:
        logger.error("未找到全局进度数据")
        return 0, "准备中...", dash.no_update
    
    progress = progress_status.get("progress", 0)
    message = progress_status.get("message", "处理中...")
    data_type = progress_status.get("type", "")
    
    logger.debug(f"进度回调触发: progress={progress}, message={message}, type={data_type}")
    
    # 确保这是股票数据的进度更新
    if data_type != "stock":
        logger.info(f"非股票数据类型: {data_type}，跳过更新")
        return dash.no_update, dash.no_update, dash.no_update
    
    # 检查是否有更新的数据（包括多进程任务的结果）
    if progress == 100 and "updated_data" in progress_status:
        logger.info(f"进度达到100%且找到updated_data，更新stocks-data-store")
        updated_data = progress_status["updated_data"]

        # 验证数据有效性
        if updated_data and isinstance(updated_data, dict):
            logger.info(f"更新数据包含 {len(updated_data)} 个股票")

            # 清空updated_data避免重复更新
            if "updated_data" in progress_status:
                del progress_status["updated_data"]

            return progress, message, updated_data
        else:
            logger.warning("警告: updated_data为空或格式不正确")
            # 清空无效数据
            if "updated_data" in progress_status:
                del progress_status["updated_data"]
    
    # 检查波动率刷新状态（当常规刷新状态不需要更新时）
    if volatility_refresh_status and volatility_refresh_status.get("updated_data") and volatility_refresh_status.get("progress") == 100:
        logger.info("检测到波动率刷新完成，更新stocks-data-store")
        updated_data = volatility_refresh_status["updated_data"]
        
        # 清空状态，避免重复更新
        volatility_refresh_status = {}
        
        return progress, message, updated_data
    
    logger.debug(f"进度未达到100%或未找到updated_data: progress={progress}, has_updated_data={'updated_data' in progress_status}")
    return progress, message, dash.no_update



@app.callback(
    [
        Output("refresh-progress-modal", "is_open", allow_duplicate=True),
        Output("refresh-progress-interval", "disabled", allow_duplicate=True)
    ],
    [Input("refresh-progress-interval", "n_intervals")],
    [State("refresh-progress-modal", "is_open"),
     State("refresh-progress-store", "data")],
    prevent_initial_call=True
)
def close_progress_modal(n_intervals, is_open, progress_data):
    # 确定要使用的全局变量
    global stock_refresh_status, new_stock_analysis_status, volatility_refresh_status
    
    # 检查progress_data中的类型，决定使用哪个全局变量
    source_type = progress_data.get("source", "refresh") if progress_data else "refresh"
    
    # 根据来源选择正确的全局变量
    if source_type == "new_stock":
        progress_status = new_stock_analysis_status
        logger.debug("关闭模态框: 使用新股票分析状态")
    elif source_type == "volatility_refresh":
        progress_status = volatility_refresh_status
        logger.debug("关闭模态框: 使用波动率刷新状态")
    else:
        progress_status = stock_refresh_status
        logger.debug("关闭模态框: 使用刷新数据状态")
    
    if not progress_status:
        logger.error("关闭模态框回调: 未找到全局进度数据")
        return is_open, False
    
    data_type = progress_status.get("type", "")
    
    # 确保这是股票数据的进度更新
    if data_type != "stock":
        logger.debug(f"关闭模态框回调: 非股票数据类型: {data_type}，跳过")
        return is_open, False
    
    progress = progress_status.get("progress", 0)
    logger.debug(f"关闭模态框回调: 进度 = {progress}, 模态框状态 = {is_open}")
    
    if progress >= 100:
        # 如果进度达到100%，2秒后关闭弹窗
        logger.debug("进度达到100%，即将关闭弹窗并禁用定时器")
        time.sleep(2)
        return False, True
    
    logger.debug(f"进度未达到100%，保持模态框 = {is_open}，定时器 = 启用")
    return is_open, False

# 添加对话框显示回调
@app.callback(
    Output("new-stock-modal", "is_open"),
    [
        Input("analyze-new", "n_clicks"),  # 修改这里: 从analyze-new-stock改为analyze-new
        Input("cancel-add-stock", "n_clicks"),
        Input("submit-add-stock", "n_clicks")
    ],
    [State("new-stock-modal", "is_open")]
)
def toggle_new_stock_modal(analyze_clicks, cancel_clicks, submit_clicks, is_open):
    ctx = dash.callback_context
    if not ctx.triggered:
        return is_open
    
    button_id = ctx.triggered[0]["prop_id"].split(".")[0]
    if button_id == "analyze-new" and analyze_clicks:  # 这里也需要修改为analyze-new
        return True
    elif button_id in ["cancel-add-stock", "submit-add-stock"]:
        return False
    return is_open

# 添加删除股票对话框显示回调
@app.callback(
    [
        Output("delete-stock-modal", "is_open"),
        Output("delete-stock-select", "options"),
        Output("delete-stock-select", "value")
    ],
    [
        Input("delete-stock", "n_clicks"),
        Input("cancel-delete-stock", "n_clicks"),
        Input("submit-delete-stock", "n_clicks")
    ],
    [
        State("delete-stock-modal", "is_open"),
        State("stocks-data-store", "data")
    ]
)
def toggle_delete_stock_modal(delete_clicks, cancel_clicks, submit_clicks, is_open, stocks_data):
    ctx = dash.callback_context
    if not ctx.triggered:
        return is_open, [], None
    
    button_id = ctx.triggered[0]["prop_id"].split(".")[0]
    
    # 生成股票选项
    stock_options = []
    if stocks_data:
        for stock_id, data in stocks_data.items():
            stock_symbol = data.get('stock_symbol', stock_id)
            stock_name = data.get('stock_name', '')
            market_type = data.get('market_type', '')
            
            # 创建选项标签
            label = f"{stock_symbol} - {stock_name}"
            if market_type:
                market_type_label = {
                    'a': 'A股',
                    'h': '港股',
                    'us': '美股',
                    'f': '期货',
                    'i': '指数',
                    'sina': '特殊指数'
                }.get(market_type, market_type.upper())
                label += f" ({market_type_label})"
            
            stock_options.append({
                "label": label,
                "value": stock_symbol
            })
    
    if button_id == "delete-stock" and delete_clicks:
        return True, stock_options, None
    elif button_id in ["cancel-delete-stock", "submit-delete-stock"]:
        return False, stock_options, None
    
    return is_open, stock_options, None

# 添加提交新股票分析的回调
@app.callback(
    [
        Output("stocks-data-store", "data", allow_duplicate=True),  # 添加allow_duplicate=True
        Output("new-stock-error", "children"),
        Output("refresh-progress-store", "data", allow_duplicate=True),
        Output("refresh-progress-modal", "is_open", allow_duplicate=True),
        Output("refresh-progress-interval", "disabled", allow_duplicate=True)
    ],
    [Input("submit-add-stock", "n_clicks")],
    [
        State("new-stock-symbol", "value"),
        State("new-stock-market-type", "value"),
        State("new-stock-index", "value"),
        State("stocks-data-store", "data")
    ],
    prevent_initial_call=True
)
def add_new_stock_analysis(n_clicks, stock_symbol, market_type, index_symbol, current_data):
    if not n_clicks:
        return current_data, "", {}, False, True
    
    # 验证输入
    if not stock_symbol:
        return current_data, "股票代码不能为空", {}, False, True
    if not market_type:
        return current_data, "请选择市场类型", {}, False, True
    if not index_symbol:
        return current_data, "指数代码不能为空", {}, False, True
    
    # 初始化全局进度数据
    global new_stock_analysis_status
    new_stock_analysis_status = {"progress": 30, "message": "股票分析中", "type": "stock", "source": "new_stock"}
    logger.info(f"开始新股票分析 {stock_symbol}")
    
    # 启动后台任务分析股票
    def background_analysis():
        global new_stock_analysis_status
        try:
            logger.info(f"开始后台分析股票: {stock_symbol}")
            db_ops = DBOperations()
            
            # 分析新股票或者指数、期货
            new_data = db_ops.add_new_stock_analysis(stock_symbol, market_type, index_symbol, new_stock_analysis_status)
            
            # 更新进度到100%
            new_stock_analysis_status["progress"] = 100
            new_stock_analysis_status["message"] = "分析完成"
            
            # 关键：将新数据添加到进度信息中
            new_stock_analysis_status["updated_data"] = new_data
            logger.info(f"股票 {stock_symbol} 分析完成，获取到 {len(new_data)} 条股票数据")
            logger.debug(f"已将updated_data添加到new_stock_analysis_status中，keys: {list(new_stock_analysis_status.keys())}")
        except Exception as e:
            # 出错时更新进度
            new_stock_analysis_status["progress"] = 100
            new_stock_analysis_status["message"] = "分析失败"
            logger.error(f"分析股票 {stock_symbol} 时出错: {str(e)}")
    
    # 启动线程
    thread = Thread(target=background_analysis)
    thread.daemon = True
    thread.start()
    
    # 返回结果，更新界面
    return current_data, "", new_stock_analysis_status, True, False

# 添加删除股票的回调
@app.callback(
    [
        Output("stocks-data-store", "data", allow_duplicate=True),
        Output("delete-stock-error", "children")
    ],
    [Input("submit-delete-stock", "n_clicks")],
    [
        State("delete-stock-select", "value"),
        State("delete-options", "value"),
        State("stocks-data-store", "data")
    ],
    prevent_initial_call=True
)
def delete_stock_analysis(n_clicks, stock_symbol, delete_options, current_data):
    if not n_clicks:
        return current_data, ""
    
    # 验证输入
    if not stock_symbol:
        return current_data, "请选择要删除的股票"
    
    try:
        # 确定是否删除分析数据
        delete_analysis_data = "delete_analysis_data" in (delete_options or [])
        
        # 使用数据库操作类删除股票
        result = db_ops.delete_stock_analysis(stock_symbol, delete_analysis_data=delete_analysis_data)
        
        if result:
            # 删除成功，更新数据存储
            logger.info(f"成功删除股票: {stock_symbol}")
            
            # 从当前数据中移除删除的股票
            updated_data = current_data.copy()
            # 查找并删除对应的股票数据
            for stock_id in list(updated_data.keys()):
                if updated_data[stock_id].get('stock_symbol') == stock_symbol:
                    del updated_data[stock_id]
                    break
            
            return updated_data, ""
        else:
            return current_data, f"删除股票 {stock_symbol} 失败，请检查股票代码是否存在"
            
    except Exception as e:
        logger.error(f"删除股票时出错: {str(e)}")
        return current_data, f"删除股票时出错: {str(e)}"

# 添加更新策略表格的回调
@app.callback(
    Output('strategy-table', 'data'),
    Output('strategy-table', 'columns'),
    [Input('market-type-dropdown', 'value')]
)
def update_strategy_table(market_type):
    """
    根据市场类型更新股票策略表格内容
    
    Args:
        market_type (str): 'A'表示A股，'H'表示港股
        
    Returns:
        tuple: (表格数据, 表格列定义)
    """
    try:
        logger.debug(f"从数据库中更新策略表格: market_type={market_type}")

        # 使用任务优化器缓存加载策略数据
        strategy_data_dict = load_strategy_data_optimized([market_type])
        strategy_data = strategy_data_dict.get(market_type)
        
        if not strategy_data:
            return [], []
        
        # 转换为数据表格格式
        data_list = []
        for stock_code, stock_info in strategy_data.items():
            if isinstance(stock_info, dict):
                data_list.append({
                    "代码": stock_code,
                    "名称": stock_info.get("name", ""),
                    "市场": stock_info.get("market", ""),
                    "价格": stock_info.get("price", 0),
                    "市值(亿)": round(stock_info.get("value", 0), 2),
                    "流通值(亿)": round(stock_info.get("flow_value", 0), 2),
                    "股息率(%)": stock_info.get("fen_hong_rate", 0),
                    "利润增长率(%)": round(stock_info.get("li_run_rate", 0), 2),
                    "收入增长率(%)": round(stock_info.get("income_rate", 0)*100, 2),
                    "现金流增长率(%)": round(stock_info.get("cash_rate", 0)*100, 2),
                    "ROE(%)": stock_info.get("roe", 0),
                    "ROE增长率(%)": round(stock_info.get("roe_rate", 0)*100, 2),
                    "市盈率": stock_info.get("pe", 0)
                })
        
        # 定义列格式
        columns = [
            {"name": "代码", "id": "代码"},
            {"name": "名称", "id": "名称"},
            {"name": "市场", "id": "市场"},
            {"name": "价格", "id": "价格"},
            {"name": "市值(亿)", "id": "市值(亿)"},
            {"name": "流通值(亿)", "id": "流通值(亿)"},
            {"name": "股息率(%)", "id": "股息率(%)"},
            {"name": "利润增长率(%)", "id": "利润增长率(%)"},
            {"name": "收入增长率(%)", "id": "收入增长率(%)"},
            {"name": "现金流增长率(%)", "id": "现金流增长率(%)"},
            {"name": "ROE(%)", "id": "ROE(%)"},
            {"name": "ROE增长率(%)", "id": "ROE增长率(%)"},
            {"name": "市盈率", "id": "市盈率"}
        ]
        
        return data_list, columns
    except Exception as e:
        logger.error(f"更新策略表格时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return [], []

@app.callback(
    [
        Output("option-refresh-progress-store", "data"),  # 修改为使用专用存储
        Output("option-refresh-progress-modal", "is_open"),  # 修改为使用专用模态框
        Output("option-refresh-progress-interval", "disabled")  # 修改为使用专用间隔组件
    ],
    [Input("refresh-option-data", "n_clicks")],
    prevent_initial_call=True
)
def refresh_option_data(n_clicks):
    if n_clicks is None:
        return dash.no_update, dash.no_update, dash.no_update
    
    # 初始化进度信息 - 简化为只显示"期权数据刷新中"
    global option_refresh_status
    option_refresh_status = {"progress": 30, "message": "期权数据刷新中", "type": "option", "source": "option"}
    logger.info(f"期权刷新按钮被点击")
    
    # 创建后台线程运行刷新函数
    def background_refresh_option():
        global option_refresh_status
        try:
            # 调用刷新期权数据的函数
            db_ops = DBOperations()
            db_ops.refresh_only_option_data()
            
            # 完成后更新进度
            option_refresh_status = {"progress": 100, "message": "刷新完成", "type": "option", "source": "option"}
            logger.info(f"期权刷新完成")
        except Exception as e:
            # 出错时更新进度
            option_refresh_status = {"progress": 100, "message": "刷新失败", "type": "option", "source": "option"}
            logger.error(f"期权刷新出错: {str(e)}")
    
    # 启动后台线程
    threading.Thread(target=background_refresh_option).start()
    
    return option_refresh_status, True, False

# 添加一个回调来更新期权刷新进度
@app.callback(
    [
        Output("option-refresh-progress-bar", "value"),
        Output("option-refresh-progress-message", "children"),
    ],
    [Input("option-refresh-progress-interval", "n_intervals")],
    [State("option-refresh-progress-store", "data")],
    prevent_initial_call=True
)
def update_option_refresh_progress(n_intervals, progress_data):
    if not progress_data:
        return 0, "准备中..."
    
    progress = progress_data.get("progress", 0)
    message = progress_data.get("message", "处理中...")
    
    return progress, message

@app.callback(
    [
        Output("option-refresh-progress-modal", "is_open", allow_duplicate=True),
        Output("option-refresh-progress-interval", "disabled", allow_duplicate=True)
    ],
    [Input("option-refresh-progress-store", "data")],
    [State("option-refresh-progress-modal", "is_open")],
    prevent_initial_call=True
)
def close_option_progress_modal(progress_data, is_open):
    if progress_data and progress_data.get("progress", 0) >= 100:
        # 如果进度达到100%，2秒后关闭弹窗
        time.sleep(2)
        return False, True
    return is_open, False

# 修改股票策略刷新按钮回调
@app.callback(
    Output("strategy-update-time", "children"),
    [Input("refresh-strategy-btn", "n_clicks")],
    prevent_initial_call=True
)
def refresh_strategy_data(n_clicks):
    """刷新股票策略数据并更新表格"""
    if not n_clicks:
        raise PreventUpdate
    
    try:
        # 刷新股票策略数据
        db_ops = DBOperations()
        db_ops.refresh_stock_strategy_data()
        
        # 返回当前时间作为更新时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        return current_time
    except Exception as e:
        logger.error(f"刷新股票策略数据出错: {str(e)}")
        import traceback
        traceback.print_exc()
        # 返回错误信息
        return f"刷新失败: {datetime.now().strftime('%H:%M:%S')}"

# 在文件末尾添加回调函数来定期更新策略数据
@app.callback(
    Output("strategy-data-store", "data"),
    [Input("strategy-refresh-interval", "n_intervals")]
)
def refresh_strategy_data_periodically(n_intervals):
    """定期从数据库获取最新策略数据"""
    try:
        # 获取最新的策略文本和更新时间
        latest_strategy = db_ops.get_current_strategy()
        latest_update_time = db_ops.get_current_strategy_update_time()
        
        return {
            "text": latest_strategy,
            "update_time": latest_update_time
        }
    except Exception as e:
        logger.error(f"定期更新策略数据出错: {str(e)}")
        return dash.no_update

@app.callback(
    [Output("current-strategy-text", "children"),
     Output("strategy-text-update-time", "children")],
    [Input("strategy-data-store", "data")]
)
def update_strategy_display(strategy_data):
    """当策略数据存储更新时，更新策略显示"""
    if not strategy_data:
        return dash.no_update, dash.no_update
    
    return strategy_data["text"], strategy_data["update_time"]

# 修改保存当前策略的回调函数，确保更新strategy-data-store
@app.callback(
    [
        Output("strategy-edit-modal", "is_open"),
        Output("current-strategy-text", "children", allow_duplicate=True),
        Output("strategy-text-update-time", "children", allow_duplicate=True),
        Output("strategy-edit-error", "children"),
        Output("strategy-text-input", "value"),
        Output("strategy-data-store", "data", allow_duplicate=True)
    ],
    [
        Input("edit-strategy-btn", "n_clicks"),
        Input("cancel-edit-strategy", "n_clicks"),
        Input("save-strategy", "n_clicks")
    ],
    [
        State("strategy-edit-modal", "is_open"),
        State("strategy-text-input", "value")
    ],
    prevent_initial_call=True
)
def handle_strategy_edit(edit_clicks, cancel_clicks, save_clicks, is_open, strategy_text):
    ctx = dash.callback_context
    if not ctx.triggered:
        return is_open, dash.no_update, dash.no_update, "", dash.no_update, dash.no_update
    
    button_id = ctx.triggered[0]["prop_id"].split(".")[0]
    
    if button_id == "edit-strategy-btn" and edit_clicks:
        return True, dash.no_update, dash.no_update, "", dash.no_update, dash.no_update
    
    elif button_id == "cancel-edit-strategy" and cancel_clicks:
        return False, dash.no_update, dash.no_update, "", dash.no_update, dash.no_update
    
    elif button_id == "save-strategy" and save_clicks:
        if not strategy_text or strategy_text.strip() == "":
            return is_open, dash.no_update, dash.no_update, "策略内容不能为空", dash.no_update, dash.no_update
        
        try:
            # 保存策略到数据库
            update_time = db_ops.save_current_strategy(strategy_text)
            
            # 更新存储的策略数据
            updated_data = {
                "text": strategy_text,
                "update_time": update_time
            }
            
            return False, strategy_text, update_time, "", strategy_text, updated_data
        except Exception as e:
            logger.error(f"保存策略时出错: {str(e)}")
            return is_open, dash.no_update, dash.no_update, f"保存失败: {str(e)}", dash.no_update, dash.no_update
    
    return is_open, dash.no_update, dash.no_update, "", dash.no_update, dash.no_update

# 升贴水数据加载回调
@app.callback(
    [
        Output("futures-gap-data-store", "data"),
        Output("futures-gap-default-display", "children"),
        Output("futures-gap-expanded-display", "children")
    ],
    [Input("load-futures-gap-data", "n_clicks")],
    prevent_initial_call=True
)
def load_futures_gap_data(n_clicks):
    """加载升贴水数据并生成动态图表"""
    if not n_clicks:
        return {}, [], []
    
    try:
        db_ops = DBOperations()
        index_types = ['50', '300', '1000']
        index_names = {'50': '上证50', '300': '沪深300', '1000': '中证1000'}
        
        # 存储所有数据
        all_data = {}
        default_display = []
        expanded_display = []
        
        logger.info("开始加载升贴水数据...")
        
        # 添加加载提示
        loading_msg = dbc.Alert([
            dbc.Spinner(size="sm"),
            html.Span("正在加载升贴水数据，请稍候...", className="ms-2")
        ], color="info", className="mb-3")
        
        for i, index_type in enumerate(index_types):
            try:
                logger.info(f"正在加载{index_names[index_type]}升贴水数据...")
                
                # 获取升贴水数据
                df, current_gap, percentile = db_ops.generate_futures_index_gap_data(index_type, picture=False)
                
                if df.empty:
                    logger.warning(f"{index_names[index_type]}数据为空，跳过")
                    continue
                
                # 计算百分比
                gap_percent = current_gap * 100
                
                # 存储数据
                all_data[index_type] = {
                    'df': df.to_dict('records'),
                    'current_gap': current_gap,
                    'gap_percent': gap_percent,
                    'percentile': percentile
                }
                
                # 创建Plotly图表
                fig = go.Figure()
                
                # 确保日期列存在并转换为datetime
                if '日期' in df.columns:
                    df['日期'] = pd.to_datetime(df['日期'])
                    
                    # 添加指数收盘价线
                    fig.add_trace(go.Scatter(
                        x=df['日期'],
                        y=df['close'],
                        name='指数收盘价',
                        line=dict(color='#1E90FF', width=2),
                        mode='lines',
                        yaxis='y'
                    ))
                    
                    # 添加期货收盘价线
                    fig.add_trace(go.Scatter(
                        x=df['日期'],
                        y=df['收盘价'],
                        name='期货收盘价',
                        line=dict(color='#2E8B57', width=2),
                        mode='lines',
                        yaxis='y'
                    ))
                    
                    # 添加升贴水柱状图（使用第二个Y轴）
                    fig.add_trace(go.Bar(
                        x=df['日期'],
                        y=df['升水'],
                        name='升贴水',
                        marker=dict(
                            color=['green' if x > 0 else 'red' for x in df['升水']],
                            opacity=0.6
                        ),
                        yaxis='y2'
                    ))
                    
                    # 设置图表布局
                    fig.update_layout(
                        title=f"{index_names[index_type]}期货升贴水分析",
                        xaxis=dict(
                            title="日期",
                            tickangle=45,
                            type='date',
                            rangeslider=dict(visible=True),
                            autorange=True
                        ),
                        yaxis=dict(
                            title="价格",
                            side='left',
                            showgrid=True
                        ),
                        yaxis2=dict(
                            title="升贴水",
                            side='right',
                            overlaying='y',
                            showgrid=False,
                            zeroline=True,
                            zerolinecolor='gray',
                            zerolinewidth=1
                        ),
                        legend=dict(
                            orientation="h",
                            yanchor="bottom",
                            y=1.02,
                            xanchor="center",
                            x=0.5
                        ),
                        showlegend=True,
                        paper_bgcolor='rgba(0,0,0,0)',
                        plot_bgcolor='rgba(0,0,0,0)',
                        font=dict(color='white'),
                        height=400,
                        margin=dict(l=20, r=20, t=40, b=60),
                        hovermode='x unified',
                        # 修复悬停标签样式
                        hoverlabel=dict(
                            bgcolor="rgba(50, 50, 50, 0.9)",  # 深灰色背景
                            bordercolor="white",              # 白色边框
                            font=dict(
                                color="white",                # 白色文字
                                size=12                       # 字体大小
                            )
                        )
                    )
                    
                    # 创建图表组件
                    chart_component = html.Div(
                        dcc.Graph(
                            figure=fig,
                            config={
                                'displayModeBar': True,
                                'scrollZoom': True,
                                'modeBarButtonsToAdd': [
                                    'select2d', 
                                    'lasso2d',
                                    'resetScale2d',
                                    'autoScale2d'
                                ],
                                'responsive': True,
                                'displaylogo': False,
                                'doubleClick': 'reset+autosize'
                            },
                            style={'height': '100%', 'width': '100%'}
                        ),
                        className="chart-container",
                        style={'height': "400px"}
                    )
                else:
                    chart_component = html.Div("数据格式错误：缺少日期列", className="text-danger")
                
                # 创建统计信息卡片
                stats_card = dbc.Card([
                    dbc.CardBody([
                        html.H6(f"{index_names[index_type]}升贴水", className="card-title"),
                        html.Hr(),
                        dbc.Row([
                            dbc.Col([
                                html.P([
                                    html.Strong("当前升贴水百分数: "),
                                    html.Span(f"{gap_percent:.2f}%", 
                                            style={"color": "green" if gap_percent > 0 else "red", "fontWeight": "bold"})
                                ], className="mb-2"),
                                html.P([
                                    html.Strong("历史分位数: "),
                                    html.Span(f"{percentile * 100:.3f}%", 
                                            style={"color": "blue", "fontWeight": "bold"})
                                ], className="mb-0")
                            ], width=12)
                        ])
                    ])
                ], className="mb-3", style={"height": "150px"})
                
                # 组合显示组件
                display_component = dbc.Row([
                    dbc.Col([stats_card], width=4),
                    dbc.Col([chart_component], width=8)
                ], className="mb-4")
                
                # 默认只显示沪深300
                if index_type == '300':
                    default_display.append(display_component)
                
                # 展开显示所有
                expanded_display.append(display_component)
                
            except Exception as e:
                logger.error(f"加载{index_type}升贴水数据时出错: {str(e)}")
                # 添加错误提示
                error_component = dbc.Alert(
                    f"加载{index_names[index_type]}数据时出错: {str(e)}", 
                    color="danger", 
                    className="mb-3"
                )
                if index_type == '300':
                    default_display.append(error_component)
                expanded_display.append(error_component)
                continue
        
        logger.info(f"升贴水数据加载完成，共加载{len(all_data)}个指数")
        return all_data, default_display, expanded_display
        
    except Exception as e:
        logger.error(f"加载升贴水数据时出错: {str(e)}")
        error_msg = dbc.Alert(f"加载数据时出错: {str(e)}", color="danger")
        return {}, [error_msg], [error_msg]

# 展开/收起升贴水显示回调
@app.callback(
    [
        Output("futures-gap-default-display", "style"),
        Output("futures-gap-expanded-display", "style"),
        Output("expand-futures-gap", "children")
    ],
    [Input("expand-futures-gap", "n_clicks")],
    [State("futures-gap-expanded-display", "style")]
)
def toggle_futures_gap_display(n_clicks, current_style):
    """切换升贴水显示模式"""
    if not n_clicks:
        return {"display": "block"}, {"display": "none"}, "展开全部"
    
    # 判断当前状态
    is_expanded = current_style and current_style.get("display") == "block"
    
    if is_expanded:
        # 当前是展开状态，切换到收起
        return {"display": "block"}, {"display": "none"}, "展开全部"
    else:
        # 当前是收起状态，切换到展开
                return {"display": "none"}, {"display": "block"}, "收起"

# 刷新升贴水缓存回调
@app.callback(
    [
        Output("futures-gap-data-store", "data", allow_duplicate=True),
        Output("futures-gap-default-display", "children", allow_duplicate=True),
        Output("futures-gap-expanded-display", "children", allow_duplicate=True)
    ],
    [Input("refresh-futures-gap-cache", "n_clicks")],
    prevent_initial_call=True
)
def refresh_futures_gap_cache(n_clicks):
    """刷新升贴水缓存"""
    if not n_clicks:
        return no_update, no_update, no_update
    
    try:
        from quantify.mongodb import delete_mongodb_one
        from quantify.option_strategy import OPTION_DB_NAME, OPTION_COLLECTION
        
        db_ops = DBOperations()
        index_types = ['50', '300', '1000']
        index_names = {'50': '上证50', '300': '沪深300', '1000': '中证1000'}
        
        logger.info("开始刷新升贴水缓存...")
        
        # 删除所有缓存
        for index_type in index_types:
            doc_id = f"{index_type}_main_futures_index_gap"
            try:
                delete_mongodb_one(doc_id, db_name=OPTION_DB_NAME, collection_name=OPTION_COLLECTION)
                logger.info(f"已删除{index_type}升贴水缓存")
            except Exception as e:
                logger.warning(f"删除{index_type}升贴水缓存失败: {str(e)}")
        
        # 重新加载数据
        all_data = {}
        default_display = []
        expanded_display = []
        
        # 添加刷新提示
        refresh_msg = dbc.Alert([
            dbc.Spinner(size="sm"),
            html.Span("正在刷新升贴水数据，请稍候...", className="ms-2")
        ], color="warning", className="mb-3")
        
        for index_type in index_types:
            try:
                logger.info(f"正在重新生成{index_names[index_type]}升贴水数据...")
                
                # 强制重新生成数据
                df, current_gap, percentile = db_ops.generate_futures_index_gap_data(index_type, picture=False)
                
                if df.empty:
                    logger.warning(f"{index_names[index_type]}数据为空，跳过")
                    continue
                
                # 计算百分比
                gap_percent = current_gap * 100
                
                # 存储数据
                all_data[index_type] = {
                    'df': df.to_dict('records'),
                    'current_gap': current_gap,
                    'gap_percent': gap_percent,
                    'percentile': percentile
                }
                
                # 创建Plotly图表（复用之前的代码）
                fig = go.Figure()
                
                if '日期' in df.columns:
                    df['日期'] = pd.to_datetime(df['日期'])
                    
                    fig.add_trace(go.Scatter(
                        x=df['日期'], y=df['close'], name='指数收盘价',
                        line=dict(color='#1E90FF', width=2), mode='lines', yaxis='y'
                    ))
                    
                    fig.add_trace(go.Scatter(
                        x=df['日期'], y=df['收盘价'], name='期货收盘价',
                        line=dict(color='#2E8B57', width=2), mode='lines', yaxis='y'
                    ))
                    
                    fig.add_trace(go.Bar(
                        x=df['日期'], y=df['升水'], name='升贴水',
                        marker=dict(color=['green' if x > 0 else 'red' for x in df['升水']], opacity=0.6),
                        yaxis='y2'
                    ))
                    
                    fig.update_layout(
                        title=f"{index_names[index_type]}期货升贴水分析",
                        xaxis=dict(title="日期", tickangle=45, type='date', rangeslider=dict(visible=True), autorange=True),
                        yaxis=dict(title="价格", side='left', showgrid=True),
                        yaxis2=dict(title="升贴水", side='right', overlaying='y', showgrid=False, zeroline=True, zerolinecolor='gray', zerolinewidth=1),
                        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="center", x=0.5),
                        showlegend=True, paper_bgcolor='rgba(0,0,0,0)', plot_bgcolor='rgba(0,0,0,0)',
                        font=dict(color='white'), height=400, margin=dict(l=20, r=20, t=40, b=60), hovermode='x unified',
                        # 修复悬停标签样式
                        hoverlabel=dict(
                            bgcolor="rgba(50, 50, 50, 0.9)",  # 深灰色背景
                            bordercolor="white",              # 白色边框
                            font=dict(color="white", size=12) # 白色文字
                        )
                    )
                    
                    chart_component = html.Div(
                        dcc.Graph(figure=fig, config={'displayModeBar': True, 'scrollZoom': True, 'responsive': True, 'displaylogo': False, 'doubleClick': 'reset+autosize'},
                                style={'height': '100%', 'width': '100%'}),
                        className="chart-container", style={'height': "400px"}
                    )
                else:
                    chart_component = html.Div("数据格式错误：缺少日期列", className="text-danger")
                
                # 创建统计信息卡片
                stats_card = dbc.Card([
                    dbc.CardBody([
                        html.H6(f"{index_names[index_type]}升贴水", className="card-title"),
                        html.Hr(),
                        dbc.Row([
                            dbc.Col([
                                html.P([
                                    html.Strong("当前升贴水百分数: "),
                                    html.Span(f"{gap_percent:.2f}%", 
                                            style={"color": "green" if gap_percent > 0 else "red", "fontWeight": "bold"})
                                ], className="mb-2"),
                                html.P([
                                    html.Strong("历史分位数: "),
                                    html.Span(f"{percentile * 100:.3f}%", 
                                            style={"color": "blue", "fontWeight": "bold"})
                                ], className="mb-0")
                            ], width=12)
                        ])
                    ])
                ], className="mb-3", style={"height": "150px"})
                
                display_component = dbc.Row([
                    dbc.Col([stats_card], width=4),
                    dbc.Col([chart_component], width=8)
                ], className="mb-4")
                
                if index_type == '300':
                    default_display.append(display_component)
                expanded_display.append(display_component)
                
            except Exception as e:
                logger.error(f"刷新{index_type}升贴水数据时出错: {str(e)}")
                error_component = dbc.Alert(f"刷新{index_names[index_type]}数据时出错: {str(e)}", color="danger", className="mb-3")
                if index_type == '300':
                    default_display.append(error_component)
                expanded_display.append(error_component)
                continue
        
        logger.info(f"升贴水缓存刷新完成，共刷新{len(all_data)}个指数")
        
        # 添加成功提示
        success_msg = dbc.Alert("升贴水数据缓存刷新成功！", color="success", className="mb-3")
        if default_display:
            default_display.insert(0, success_msg)
        if expanded_display:
            expanded_display.insert(0, success_msg)
        
        return all_data, default_display, expanded_display
        
    except Exception as e:
        logger.error(f"刷新升贴水缓存时出错: {str(e)}")
        error_msg = dbc.Alert(f"刷新缓存时出错: {str(e)}", color="danger")
        return {}, [error_msg], [error_msg]

# 生成升贴水分析报告回调
@app.callback(
    [
        Output("basis-analysis-report-store", "data"),
        Output("basis-analysis-report-display", "children"),
        Output("basis-analysis-report-display", "style")
    ],
    [Input("generate-basis-analysis-report", "n_clicks")],
    prevent_initial_call=True
)
def generate_basis_analysis_report(n_clicks):
    """生成升贴水分析报告"""
    if not n_clicks:
        return {}, [], {"display": "none"}
    
    try:
        logger.info("开始生成升贴水分析报告...")

        # 显示加载提示
        loading_display = [
            dbc.Alert([
                dbc.Spinner(size="sm"),
                html.Span("正在生成升贴水分析报告，请稍候...", className="ms-2")
            ], color="info", className="mb-3")
        ]

        # 使用任务优化器处理CPU密集型的报告生成任务
        optimizer = get_task_optimizer()

        def basis_report_generation_task():
            # 创建报告生成器
            generator = BasisReportGenerator()

            # 运行所有指数的分析
            generator.run_all_analysis()

            # 生成Markdown报告
            report_path = generator.generate_markdown_report()

            # 读取生成的Markdown内容
            with open(report_path, 'r', encoding='utf-8') as f:
                markdown_content = f.read()

            return generator, report_path, markdown_content

        # 异步执行报告生成任务
        future = optimizer.optimize_task("basis_report_generation", basis_report_generation_task)
        generator, report_path, markdown_content = future.result()

        # 创建美观的报告显示
        report_display = create_beautiful_basis_report(generator.report_data, report_path)

        # 存储报告数据
        report_data = {
            "markdown_content": markdown_content,
            "report_path": report_path,
            "generated_time": datetime.now().isoformat(),
            "analysis_results": generator.report_data
        }

        logger.info(f"升贴水分析报告生成完成: {report_path}")

        return report_data, report_display, {"display": "block"}
        
    except Exception as e:
        logger.error(f"生成升贴水分析报告时出错: {str(e)}")
        error_display = [
            dbc.Alert(f"生成分析报告时出错: {str(e)}", color="danger", className="mb-3")
        ]
        return {}, error_display, {"display": "block"}

# 查看升贴水分析报告回调
@app.callback(
    [
        Output("basis-analysis-report-display", "children", allow_duplicate=True),
        Output("basis-analysis-report-display", "style", allow_duplicate=True)
    ],
    [Input("view-basis-analysis-report", "n_clicks")],
    [State("basis-analysis-report-store", "data")],
    prevent_initial_call=True
)
def view_basis_analysis_report(n_clicks, report_data):
    """查看已生成的升贴水分析报告"""
    if not n_clicks:
        return [], {"display": "none"}
    
    if not report_data or "analysis_results" not in report_data:
        no_report_display = [
            dbc.Alert("暂无分析报告，请先点击\"生成分析报告\"按钮", color="warning", className="mb-3")
        ]
        return no_report_display, {"display": "block"}
    
    try:
        # 显示已生成的报告
        report_display = [
            dbc.Alert([
                html.Strong("报告生成时间: "),
                html.Span(report_data.get("generated_time", "未知"))
            ], color="info", className="mb-3")
        ]
        
        # 创建美观的报告显示
        beautiful_report = create_beautiful_basis_report(
            report_data["analysis_results"], 
            report_data.get("report_path", "")
        )
        
        report_display.extend(beautiful_report)
        
        return report_display, {"display": "block"}
        
    except Exception as e:
        logger.error(f"查看升贴水分析报告时出错: {str(e)}")
        error_display = [
            dbc.Alert(f"查看分析报告时出错: {str(e)}", color="danger", className="mb-3")
        ]
        return error_display, {"display": "block"}

def create_beautiful_basis_report(report_data, report_path):
    """创建美观的升贴水分析报告显示"""
    
    components = []
    
    # 报告标题
    components.append(
        dbc.Card([
            dbc.CardHeader([
                html.H4("中国股指期货升贴水综合分析报告", className="text-center mb-0"),
                html.Small(f"报告路径: {report_path}", className="text-muted text-center d-block mt-2")
            ])
        ], className="mb-4")
    )
    
    # 1. 基本统计信息对比
    if report_data:
        components.append(component_renderers.create_basic_stats_section(report_data))
        
        # 2. 相关性分析
        components.append(component_renderers.create_correlation_section(report_data))
        
        # 3. 升贴水水平分析
        components.append(component_renderers.create_level_analysis_section(report_data))
        
        # 4. 交易信号分析
        components.append(create_trading_signals_section(report_data))
        
        # 5. 主要发现
        components.append(create_key_findings_section(report_data))
    
    return components

# create_basic_stats_section 函数已移动到 component_renderers.py

# create_correlation_section 函数已移动到 component_renderers.py

# create_level_analysis_section 函数已移动到 component_renderers.py

def create_trading_signals_section(report_data):
    """创建交易信号分析部分"""
    
    # 创建主标题卡片
    title_card = dbc.Card([
        dbc.CardHeader([
            html.H5("4. 交易信号分析", className="mb-0")
        ]),
        dbc.CardBody([
            html.P("基于升贴水的交易信号效果分析：", className="mb-3")
        ])
    ], className="mb-3")
    
    # 创建所有交易信号表格
    signal_cards = []
    
    # 为每个指数创建交易信号表格
    for index_type in ['50', '300', '1000']:
        if index_type in report_data and 'trading_signals' in report_data[index_type]:
            index_name = {'50': '上证50', '300': '沪深300', '1000': '中证1000'}[index_type]
            signal_data = report_data[index_type]['trading_signals']
            
            # 准备表格数据
            table_data = []
            for signal, perf in signal_data.items():
                table_data.append({
                    '信号': signal,
                    '次数': perf['count'],
                    '5日均收益': f"{perf['mean_return_5d']:.4f}%",
                    '20日均收益': f"{perf['mean_return_20d']:.4f}%",
                    '30日均收益': f"{perf['mean_return_30d']:.4f}%",
                    '40日均收益': f"{perf['mean_return_40d']:.4f}%",
                    '5日胜率': f"{perf['win_rate_5d']*100:.1f}%",
                    '20日胜率': f"{perf['win_rate_20d']*100:.1f}%"
                })
            
            table_columns = [
                {"name": "信号", "id": "信号"},
                {"name": "次数", "id": "次数"},
                {"name": "5日均收益", "id": "5日均收益"},
                {"name": "20日均收益", "id": "20日均收益"},
                {"name": "30日均收益", "id": "30日均收益"},
                {"name": "40日均收益", "id": "40日均收益"},
                {"name": "5日胜率", "id": "5日胜率"},
                {"name": "20日胜率", "id": "20日胜率"}
            ]
            
            signal_cards.append(
                dbc.Card([
                    dbc.CardHeader([
                        html.H6(f"{index_name}指数交易信号效果", className="mb-0")
                    ]),
                    dbc.CardBody([
                        dash_table.DataTable(
                            data=table_data,
                            columns=table_columns,
                            style_table={'overflowX': 'auto'},
                            style_cell={
                                'textAlign': 'center',
                                'padding': '8px',
                                'backgroundColor': '#2b3e50',
                                'color': 'white',
                                'border': '1px solid #495057'
                            },
                            style_header={
                                'backgroundColor': '#1e2a3a',
                                'fontWeight': 'bold',
                                'color': 'white'
                            },
                            style_data_conditional=[
                                {
                                    'if': {'row_index': 'odd'},
                                    'backgroundColor': '#34495e'
                                },
                                {
                                    'if': {
                                        'filter_query': '{信号} = 买入信号',
                                        'column_id': '信号'
                                    },
                                    'backgroundColor': '#28a745',
                                    'color': 'white'
                                },
                                {
                                    'if': {
                                        'filter_query': '{信号} = 卖出信号',
                                        'column_id': '信号'
                                    },
                                    'backgroundColor': '#dc3545',
                                    'color': 'white'
                                }
                            ]
                        )
                    ])
                ], className="mb-3")
            )
    
    # 返回包含所有组件的Div
    return html.Div([title_card] + signal_cards)

def create_key_findings_section(report_data):
    """创建主要发现部分"""
    
    findings = []
    
    for index_type in ['50', '300', '1000']:
        if index_type in report_data:
            index_name = {'50': '上证50', '300': '沪深300', '1000': '中证1000'}[index_type]
            
            # 分析相关性
            if 'correlation_analysis' in report_data[index_type]:
                corr_data = report_data[index_type]['correlation_analysis']
                if corr_data:
                    best_corr_period = max(corr_data.keys(), 
                                          key=lambda x: abs(corr_data[x]['pearson_corr']))
                    best_corr = corr_data[best_corr_period]['pearson_corr']
                    
                    findings.append(
                        html.Li([
                            html.Strong(f"{index_name}："),
                            f"升贴水与{best_corr_period}日后收益率相关性最强: {best_corr:.4f}"
                        ])
                    )
                    
                    if abs(best_corr) > 0.1:
                        direction = "负相关" if best_corr < 0 else "正相关"
                        findings.append(
                            html.Li(f"升贴水与未来收益率呈现{direction}关系")
                        )
            
            # 分析最佳升贴水水平
            if 'level_analysis' in report_data[index_type]:
                level_data = report_data[index_type]['level_analysis']
                if level_data:
                    best_level = max(level_data.keys(), 
                                   key=lambda x: level_data[x]['mean_future_return_20d'])
                    worst_level = min(level_data.keys(), 
                                    key=lambda x: level_data[x]['mean_future_return_20d'])
                    
                    findings.append(
                        html.Li([
                            html.Strong(f"{index_name}："),
                            f"{best_level}时20日后平均收益率最高: {level_data[best_level]['mean_future_return_20d']:.4f}%"
                        ])
                    )
                    findings.append(
                        html.Li([
                            html.Strong(f"{index_name}："),
                            f"{worst_level}时20日后平均收益率最低: {level_data[worst_level]['mean_future_return_20d']:.4f}%"
                        ])
                    )
    
    return dbc.Card([
        dbc.CardHeader([
            html.H5("5. 主要发现", className="mb-0")
        ]),
        dbc.CardBody([
            html.Ul(findings, className="mb-3"),
            
            dbc.Alert([
                html.H6("投资建议", className="alert-heading"),
                html.Hr(),
                html.P("基于以上分析，我们提出以下投资建议："),
                html.Ul([
                    html.Li("升贴水监控: 密切关注升贴水的历史分位数，当处于极值时考虑反向操作"),
                    html.Li("多周期验证: 结合5日、20日、30日、40日等多个周期的收益率预测进行决策"),
                    html.Li("指数差异化: 不同指数的升贴水特征存在差异，需要分别制定策略"),
                    html.Li("风险控制: 升贴水虽有一定预测能力，但不应作为唯一决策依据")
                ])
            ], color="info"),
            
            dbc.Alert([
                html.H6("风险提示", className="alert-heading"),
                html.Hr(),
                html.Ul([
                    html.Li("历史数据不代表未来表现"),
                    html.Li("市场环境变化可能影响升贴水的预测效果"),
                    html.Li("建议结合其他技术指标和基本面分析"),
                    html.Li("注意控制仓位和风险敞口")
                ])
            ], color="warning")
        ])
    ], className="mb-4")

server = app.server

# 全局调度器变量
scheduler = None

def scheduled_volatility_refresh():
    """
    定时刷新波动率分析的任务函数 - 使用db_operations统一管理
    """
    try:
        logger.info("开始执行定时波动率分析刷新...")
        
        # 调用db_operations中的方法
        result = db_ops.run_volatility_analysis_update()
        
        if result and result.get("success_count", 0) > 0:
            # 设置全局变量来触发界面数据更新
            global volatility_refresh_status
            volatility_refresh_status = {
                "progress": 100,
                "message": f"波动率分析刷新完成 (成功: {result['success_count']}, 失败: {result['error_count']})",
                "type": "stock",
                "source": "volatility_refresh",
                "updated_data": result["updated_data"]
            }
            
            logger.info("已设置volatility_refresh_status，将触发界面数据更新")
        
    except Exception as e:
        logger.error(f"定时波动率分析刷新执行失败: {e}")

def scheduled_option_trade_signal_update():
    """
    定时执行期权交易信号更新的任务函数
    """
    try:
        logger.info("开始执行定时期权交易信号更新...")
        
        # 调用db_operations中的期权交易信号更新方法
        result = db_ops.option_trade_signal_update()
        
        if result:
            logger.info("期权交易信号更新成功完成")
        else:
            logger.warning("期权交易信号更新失败")
            traceback.print_exc()
            
    except Exception as e:
        logger.error(f"定时期权交易信号更新执行失败: {e}")

def is_trading_day(dt=None):
    """
    判断是否为交易日（工作日，排除周末）
    TODO: 可以扩展为排除节假日
    """
    if dt is None:
        dt = datetime.now(pytz.timezone('Asia/Shanghai'))
    
    # 0=周一, 6=周日
    return dt.weekday() < 5  # 周一到周五

def start_scheduler():
    """
    启动定时任务调度器
    """
    global scheduler
    
    try:
        if scheduler is not None:
            logger.warning("调度器已经启动")
            return
        
        # 创建调度器
        scheduler = BackgroundScheduler(timezone=pytz.timezone('Asia/Shanghai'))
        
        # 添加波动率分析定时任务
        # 每个工作日早上10点执行
        scheduler.add_job(
            func=scheduled_volatility_refresh,
            trigger=CronTrigger(
                hour=10,        # 早上10点
                minute=0,       # 0分
                second=0,       # 0秒
                day_of_week='mon-fri',  # 周一到周五
                timezone=pytz.timezone('Asia/Shanghai')
            ),
            id='volatility_refresh_daily',
            name='每日波动率分析刷新',
            max_instances=1,  # 确保同时只有一个实例运行
            coalesce=True,    # 如果有多个任务排队，只保留最新的一个
            misfire_grace_time=300  # 允许5分钟的误差
        )
        
        # 添加期权交易信号更新定时任务
        # 工作日9:30-15:00之间每30分钟执行一次
        # 设置具体的分钟数：30, 50, 10, 30, 50 (每小时的30分和50分，以及下一小时的10分)
        
        scheduler.add_job(
            func=scheduled_option_trade_signal_update,
            trigger=CronTrigger(
                hour='10-11,13-14',    # 10点到11点，下午1点到2点
                minute='25,45',  # 每小时的25分、45分
                second=0,       # 0秒
                day_of_week='mon-fri',  # 周一到周五
                timezone=pytz.timezone('Asia/Shanghai')
            ),
            id='option_trade_signal_update_regular',
            name='期权交易信号定时更新',
            max_instances=1,  # 确保同时只有一个实例运行
            coalesce=True,    # 如果有多个任务排队，只保留最新的一个
            misfire_grace_time=300  # 允许5分钟的误差
        )
        
        # 特殊处理9:30的首次执行
        scheduler.add_job(
            func=scheduled_option_trade_signal_update,
            trigger=CronTrigger(
                hour=9,         # 9点
                minute=35,      # 35分
                second=0,       # 0秒
                day_of_week='mon-fri',  # 周一到周五
                timezone=pytz.timezone('Asia/Shanghai')
            ),
            id='option_trade_signal_update_start',
            name='期权交易信号开盘更新',
            max_instances=1,  # 确保同时只有一个实例运行
            coalesce=True,    # 如果有多个任务排队，只保留最新的一个
            misfire_grace_time=300  # 允许5分钟的误差
        )
        
        # 启动调度器
        scheduler.start()
        logger.info("定时任务调度器启动成功")
        logger.info("- 每个工作日上午10:00自动刷新波动率分析")
        logger.info("- 每个工作日9:30-15:00期间每20分钟执行期权交易信号更新")
        
        # 打印下次执行时间
        for job in scheduler.get_jobs():
            if job.next_run_time:
                logger.info(f"任务 [{job.name}] 下次执行时间: {job.next_run_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            
    except Exception as e:
        logger.error(f"启动定时任务调度器失败: {e}")
        print(f"启动定时任务调度器失败: {e}")

def stop_scheduler():
    """
    停止定时任务调度器
    """
    global scheduler
    
    try:
        if scheduler is not None:
            scheduler.shutdown(wait=False)
            scheduler = None
            logger.info("定时任务调度器已停止")
    except Exception as e:
        logger.error(f"停止定时任务调度器失败: {e}")

def get_scheduler_status():
    """
    获取调度器状态信息
    """
    global scheduler
    
    if scheduler is None:
        return {
            "status": "stopped",
            "jobs": [],
            "next_run": None
        }
    
    try:
        jobs_info = []
        for job in scheduler.get_jobs():
            jobs_info.append({
                "id": job.id,
                "name": job.name,
                "next_run": job.next_run_time.strftime('%Y-%m-%d %H:%M:%S %Z') if job.next_run_time else None,
                "func": job.func.__name__ if hasattr(job, 'func') else None
            })
        
        return {
            "status": "running" if scheduler.running else "stopped",
            "jobs": jobs_info,
            "timezone": str(scheduler.timezone)
        }
        
    except Exception as e:
        logger.error(f"获取调度器状态失败: {e}")
        return {
            "status": "error",
            "error": str(e),
            "jobs": []
        }

# 添加调试 stocks_data 的回调函数
@app.callback(
    [
        Output("debug-stocks-data-modal", "is_open"),
        Output("debug-stocks-data-content", "children")
    ],
    [
        Input("debug-stocks-data-btn", "n_clicks"),
        Input("close-debug-modal", "n_clicks")
    ],
    [
        State("debug-stocks-data-modal", "is_open"),
        State("stocks-data-store", "data")
    ]
)
def toggle_debug_modal(debug_btn_clicks, close_btn_clicks, is_open, stocks_data):
    """切换调试模态框并显示 stocks_data 内容"""
    ctx = callback_context
    if not ctx.triggered:
        return False, ""

    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]

    if trigger_id == "debug-stocks-data-btn" and debug_btn_clicks:
        # 格式化 stocks_data 内容
        import json
        try:
            formatted_content = json.dumps(stocks_data, indent=2, ensure_ascii=False)

            # 添加一些统计信息
            stats = f"""=== stocks_data 统计信息 ===
总股票数量: {len(stocks_data) if stocks_data else 0}
数据类型: {type(stocks_data)}
数据来源: db_ops.get_all_stocks()

=== 详细内容 ===
{formatted_content}
"""
            return True, stats
        except Exception as e:
            error_content = f"""=== 错误信息 ===
无法格式化 stocks_data: {str(e)}

=== 原始数据 ===
{str(stocks_data)}
"""
            return True, error_content

    elif trigger_id == "close-debug-modal":
        return False, ""

    return is_open, ""

# 应用启动时预加载数据
def initialize_app():
    """应用初始化"""
    logger.info("应用启动，开始初始化...")

    # 1. 数据预加载（可选，失败不影响其他功能）
    try:
        db_ops = DBOperations()
        db_ops.preload_futures_gap_data()
        logger.info("升贴水数据预加载已启动（使用装饰器优化）")
    except Exception as e:
        logger.error(f"数据预加载失败: {str(e)}")

    # 2. 启动自动调度器（可选，失败不影响其他功能）
    try:
        initialize_auto_scheduler()
        logger.info("自动更新调度器已启动 -- 刷新数据和策略更新")
    except Exception as e:
        logger.error(f"自动调度器启动失败: {str(e)}")

    # 3. 启动定时任务调度器（重要，必须启动）
    try:
        start_scheduler()
        logger.info("定时任务调度器启动成功")
    except Exception as e:
        logger.error(f"定时任务调度器启动失败: {str(e)}")
        # 即使失败也继续执行其他初始化步骤

    # 4. 注册期权分析回调函数（可选）
    try:
        register_option_analysis_callbacks(app)
        logger.info("期权分析回调函数已注册")
    except Exception as e:
        logger.error(f"期权分析回调函数注册失败: {str(e)}")

    # 5. 注册机器分析回调函数（可选）
    try:
        register_machine_analysis_callbacks(app)
        logger.info("机器分析回调函数已注册")
    except Exception as e:
        logger.error(f"机器分析回调函数注册失败: {str(e)}")

    # 6. 注册折线图回调函数（可选）
    try:
        register_line_chart_callbacks(app)
        logger.info("折线图回调函数已注册")
    except Exception as e:
        logger.error(f"折线图回调函数注册失败: {str(e)}")

    logger.info("应用初始化完成")


# 机器分析页面内容渲染回调
@app.callback(
    Output("machine-analysis-main-content", "children"),
    Input("machine-analysis-btn", "n_clicks")
)
def render_machine_analysis_content(n_clicks):
    """渲染机器分析页面内容"""
    if n_clicks:
        return create_machine_analysis_layout()
    return html.Div()


# 折线图页面内容渲染回调
@app.callback(
    Output("line-chart-main-content", "children"),
    Input("line-chart-btn", "n_clicks")
)
def render_line_chart_content(n_clicks):
    """渲染折线图页面内容"""
    if n_clicks:
        return create_line_chart_layout()
    return html.Div()


# 期权分析卡片渲染回调
@app.callback(
    Output("historical-data-card-container", "children"),
    Input("option-analysis-btn", "n_clicks")
)
def render_historical_data_card(n_clicks):
    """渲染历史数据分析卡片"""
    if n_clicks:
        return create_historical_data_card()
    return html.Div()


@app.callback(
    Output("volatility-structure-card-container", "children"),
    Input("option-analysis-btn", "n_clicks")
)
def render_volatility_structure_card(n_clicks):
    """渲染波动率结构分析卡片"""
    if n_clicks:
        return create_volatility_structure_card()
    return html.Div()


@app.callback(
    Output("investment-tips-card-container", "children"),
    Input("option-analysis-btn", "n_clicks")
)
def render_investment_tips_card(n_clicks):
    """渲染投资提示卡片"""
    if n_clicks:
        return create_investment_tips_card()
    return html.Div()


@app.callback(
    [
        Output("task-queue-modal", "is_open"),
        Output("task-queue-update-interval", "disabled")  # 使用修改后的ID
    ],
    [
        Input("nav-task-queue-btn", "n_clicks"),  # 使用导航栏按钮的ID
        Input("close-task-queue-btn", "n_clicks")  # 使用修改后的ID
    ],
    [State("task-queue-modal", "is_open")]
)
def toggle_task_queue_modal(show_clicks, close_clicks, is_open):
    """切换任务队列模态框"""
    if show_clicks or close_clicks:
        new_state = not is_open
        return new_state, not new_state  # 模态框打开时启用定时器
    return is_open, not is_open

@app.callback(
    Output("task-queue-data-store", "data"),  
    [
        Input("task-queue-update-interval", "n_intervals"),  
        Input("refresh-task-queue-btn", "n_clicks")  
    ]
)
def update_task_queue_data(n_intervals, refresh_clicks):
    """更新任务队列数据 - 集成多进程任务"""
    try:
        # 获取传统任务队列数据
        task_data = global_task_queue.get_all_tasks()

        # 获取多进程任务数据
        try:
            from quantify.dashview.async_task_manager import get_task_manager
            multiprocess_manager = get_task_manager()

            # 获取多进程任务
            mp_tasks = multiprocess_manager.get_all_tasks()
            mp_completed = multiprocess_manager.completed_tasks

            # 转换多进程任务格式以匹配现有显示
            for task_id, task_info in mp_tasks.items():
                mp_task = {
                    "task_id": task_id,
                    "task_name": f"[多进程] {task_info.task_name}",
                    "task_type": task_info.task_type,
                    "status": task_info.status.value,
                    "created_time": task_info.created_time,
                    "start_time": task_info.start_time,
                    "end_time": task_info.end_time,
                    "progress": task_info.progress,
                    "details": f"进度: {task_info.progress:.1f}%",
                    "success": task_info.status.value == "completed",
                    "worker_pid": task_info.worker_pid,
                    "estimated_duration": task_info.estimated_duration
                }
                task_data["current"].append(mp_task)

            # 添加已完成的多进程任务
            for task_info in mp_completed[-10:]:  # 只显示最近10个
                mp_task = {
                    "task_id": task_info.task_id,
                    "task_name": f"[多进程] {task_info.task_name}",
                    "task_type": task_info.task_type,
                    "status": task_info.status.value,
                    "created_time": task_info.created_time,
                    "start_time": task_info.start_time,
                    "end_time": task_info.end_time,
                    "progress": task_info.progress,
                    "details": f"完成时间: {task_info.end_time.strftime('%H:%M:%S') if task_info.end_time else 'N/A'}",
                    "success": task_info.status.value == "completed",
                    "worker_pid": task_info.worker_pid,
                    "estimated_duration": task_info.estimated_duration
                }
                task_data["completed"].append(mp_task)

        except Exception as mp_error:
            logger.warning(f"获取多进程任务数据失败: {str(mp_error)}")

        logger.info(f"获取任务队列数据: 当前任务{len(task_data.get('current', []))}, 完成任务{len(task_data.get('completed', []))}")
        return task_data
    except Exception as e:
        logger.error(f"获取任务队列数据失败: {str(e)}")
        return {"current": [], "completed": []}

@app.callback(
    Output("task-queue-content", "children"),
    [Input("task-queue-data-store", "data")]
)
def render_task_queue_content(task_data):
    """渲染任务队列内容"""
    if not task_data:
        return html.Div([
            html.H5("暂无任务数据", className="text-center text-muted"),
            html.P("请稍后再试或刷新状态", className="text-center text-muted")
        ], className="p-4")
    
    current_tasks = task_data.get("current", [])
    completed_tasks = task_data.get("completed", [])
    
    content = []
    
    # 添加调度器状态信息
    scheduler_status = get_scheduler_status()
    scheduler_card = dbc.Card([
        dbc.CardHeader([
            html.H6([
                html.I(className="fas fa-clock text-primary me-2"),
                "定时任务调度器状态"
            ], className="mb-0")
        ]),
        dbc.CardBody([
            dbc.Row([
                dbc.Col([
                    html.Div([
                        html.Strong("状态: "),
                        html.Span(
                            scheduler_status["status"],
                            className=f"badge bg-{'success' if scheduler_status['status'] == 'running' else 'danger'}"
                        )
                    ], className="mb-2"),
                    html.Div([
                        html.Strong("时区: "),
                        html.Span(scheduler_status.get("timezone", "未知"))
                    ], className="mb-2") if scheduler_status.get("timezone") else None
                ], width=12)
            ]),
            # 显示定时任务列表
            html.Div([
                html.H6("定时任务列表:", className="mt-3 mb-2"),
                html.Div([
                    dbc.ListGroup([
                        dbc.ListGroupItem([
                            html.Div([
                                html.Strong(job["name"]),
                                html.Small(f" (ID: {job['id']})", className="text-muted ms-2")
                            ]),
                            html.Div([
                                html.I(className="fas fa-calendar-alt text-info me-1"),
                                html.Small(f"下次执行: {job.get('next_run', '未知')}", className="text-muted")
                            ]) if job.get('next_run') else html.Small("未安排执行时间", className="text-warning")
                        ]) for job in scheduler_status.get("jobs", [])
                    ]) if scheduler_status.get("jobs") else html.P("暂无定时任务", className="text-muted")
                ])
            ]) if scheduler_status["status"] == "running" else None
        ])
    ], className="mb-3")
    content.append(scheduler_card)

    # 添加多进程系统监控信息
    try:
        from quantify.dashview.async_task_manager import get_task_manager
        multiprocess_manager = get_task_manager()
        system_stats = multiprocess_manager.get_system_stats()

        multiprocess_card = dbc.Card([
            dbc.CardHeader([
                html.H6([
                    html.I(className="fas fa-microchip text-success me-2"),
                    "多进程系统监控"
                ], className="mb-0")
            ]),
            dbc.CardBody([
                dbc.Row([
                    dbc.Col([
                        html.Div([
                            html.H5(f"{system_stats['cpu_usage']:.1f}%", className="text-primary mb-0"),
                            html.P("CPU使用率", className="small text-muted mb-0")
                        ], className="text-center")
                    ], width=3),
                    dbc.Col([
                        html.Div([
                            html.H5(f"{system_stats['memory_usage']:.1f}%", className="text-info mb-0"),
                            html.P("内存使用率", className="small text-muted mb-0")
                        ], className="text-center")
                    ], width=3),
                    dbc.Col([
                        html.Div([
                            html.H5(f"{system_stats['active_tasks']}", className="text-warning mb-0"),
                            html.P("活跃任务", className="small text-muted mb-0")
                        ], className="text-center")
                    ], width=3),
                    dbc.Col([
                        html.Div([
                            html.H5(f"{system_stats['completed_tasks']}", className="text-success mb-0"),
                            html.P("已完成", className="small text-muted mb-0")
                        ], className="text-center")
                    ], width=3)
                ])
            ])
        ], className="mb-3")
        content.append(multiprocess_card)

    except Exception as e:
        logger.warning(f"获取多进程系统监控信息失败: {str(e)}")

    # 添加统计信息
    stats_card = dbc.Card([
        dbc.CardBody([
            dbc.Row([
                dbc.Col([
                    html.H4(len(current_tasks), className="text-info mb-0"),
                    html.P("当前任务", className="small text-muted mb-0")
                ], width=6, className="text-center"),
                dbc.Col([
                    html.H4(len(completed_tasks), className="text-success mb-0"),
                    html.P("已完成", className="small text-muted mb-0")
                ], width=6, className="text-center")
            ])
        ])
    ], className="mb-3")
    content.append(stats_card)
    
    # 当前任务区域
    if current_tasks:
        current_section = html.Div([
            html.H5([
                html.I(className="fas fa-play-circle text-info me-2"),
                "正在运行的任务"
            ], className="mb-3"),
            html.Div([
                create_task_item(task, is_current=True) for task in current_tasks
            ])
        ], className="mb-4")
        content.append(current_section)
    
    # 已完成任务区域
    if completed_tasks:
        # 只显示最近的10个已完成任务
        recent_completed = completed_tasks[-10:] if len(completed_tasks) > 10 else completed_tasks
        completed_section = html.Div([
            html.H5([
                html.I(className="fas fa-check-circle text-success me-2"),
                f"最近完成的任务 ({len(recent_completed)}/{len(completed_tasks)})"
            ], className="mb-3"),
            html.Div([
                create_task_item(task, is_current=False) for task in reversed(recent_completed)
            ])
        ])
        content.append(completed_section)
    
    # 如果没有任何任务
    if not current_tasks and not completed_tasks:
        content.append(
            html.Div([
                html.I(className="fas fa-inbox fa-3x text-muted mb-3"),
                html.H5("暂无任务", className="text-muted"),
                html.P("当前没有运行中或已完成的任务", className="text-muted")
            ], className="text-center py-5")
        )
    
    return html.Div(content)

def create_task_item(task, is_current=True):
    """创建单个任务项"""
    # 解析任务数据
    task_id = task.get('task_id', '')
    task_name = task.get('task_name', '未知任务')
    task_type = task.get('task_type', '')
    status = task.get('status', 'unknown')
    progress = task.get('progress', 0)
    details = task.get('details', '')
    start_time = task.get('start_time', '')
    end_time = task.get('end_time', '')
    success = task.get('success', True)
    worker_pid = task.get('worker_pid', None)
    estimated_duration = task.get('estimated_duration', None)

    # 检查是否为多进程任务
    is_multiprocess = task_name.startswith('[多进程]')
    
    # 状态图标和颜色
    if is_current:
        if status == 'running':
            icon = "fas fa-spinner fa-spin"
            color = "info"
            status_text = "运行中"
        elif status == 'pending':
            icon = "fas fa-clock"
            color = "warning"
            status_text = "等待中"
        else:
            icon = "fas fa-question"
            color = "secondary"
            status_text = status
    else:
        if success:
            icon = "fas fa-check-circle"
            color = "success"
            status_text = "已完成"
        else:
            icon = "fas fa-times-circle"
            color = "danger"
            status_text = "失败"
    
    # 格式化时间
    def format_time(time_str):
        if not time_str:
            return ""
        try:
            from datetime import datetime
            if isinstance(time_str, str):
                dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
            else:
                dt = time_str
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return str(time_str)
    
    # 计算任务耗时
    duration_text = ""
    if start_time and end_time:
        try:
            from datetime import datetime
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            duration = end_dt - start_dt
            duration_text = f"耗时: {str(duration).split('.')[0]}"
        except:
            pass
    
    # 进度条组件（仅对当前任务显示）
    progress_component = None
    if is_current and progress > 0:
        progress_component = dbc.Progress(
            value=progress,
            label=f"{progress}%",
            color="info",
            className="mb-2",
            style={"height": "20px"}
        )
    
    return dbc.Card([
        dbc.CardBody([
            dbc.Row([
                dbc.Col([
                    html.Div([
                        html.I(className=f"{icon} me-2"),
                        html.Strong(task_name),
                        dbc.Badge(status_text, color=color, className="ms-2")
                    ], className="mb-2"),
                    
                    # 任务详情
                    html.Div([
                        html.Small([
                            html.Strong("任务ID: "), task_id
                        ], className="text-muted d-block"),
                        html.Small([
                            html.Strong("类型: "), task_type
                        ], className="text-muted d-block") if task_type else None,
                        html.Small([
                            html.Strong("详情: "), details
                        ], className="text-muted d-block") if details else None,
                        # 多进程任务的特殊信息
                        html.Small([
                            html.I(className="fas fa-microchip me-1"),
                            html.Strong("工作进程: "), f"PID {worker_pid}"
                        ], className="text-info d-block") if is_multiprocess and worker_pid else None,
                        html.Small([
                            html.I(className="fas fa-hourglass-half me-1"),
                            html.Strong("预估时长: "), f"{estimated_duration:.0f}秒"
                        ], className="text-muted d-block") if is_multiprocess and estimated_duration else None,
                    ]),
                    
                    # 进度条
                    progress_component,
                    
                    # 时间信息
                    html.Div([
                        html.Small([
                            html.I(className="fas fa-clock me-1"),
                            "开始: ", format_time(start_time)
                        ], className="text-muted d-block") if start_time else None,
                        html.Small([
                            html.I(className="fas fa-flag-checkered me-1"),
                            "完成: ", format_time(end_time)
                        ], className="text-muted d-block") if end_time else None,
                        html.Small(duration_text, className="text-muted d-block") if duration_text else None,
                    ], className="mt-2")
                ])
            ])
        ])
    ], className="mb-2", outline=True, color=color if is_current else None)

@app.callback(
    Output("task-queue-data-store", "data", allow_duplicate=True),  # 使用修改后的ID
    [Input("clear-task-history-btn", "n_clicks")],  # 使用修改后的ID
    prevent_initial_call=True
)
def clear_task_history_new(n_clicks):  # 重命名函数避免重复
    """清空任务历史"""
    if n_clicks:
        try:
            # 清空已完成任务历史
            global_task_queue.completed_tasks = []
            return {"current": list(global_task_queue.current_tasks.values()), "completed": []}
        except Exception as e:
            logger.error(f"清空任务历史失败: {str(e)}")
    return dash.no_update

# 波动率分析按钮回调函数
@app.callback(
    Output({"type": "volatility-analysis-container", "symbol": MATCH}, "children"),
    [Input({"type": "volatility-analysis-btn", "symbol": MATCH}, "n_clicks")],
    prevent_initial_call=True
)
def handle_volatility_analysis_btn(n_clicks):
    """处理波动率分析按钮点击"""
    if not n_clicks:
        return []
    
    # 获取股票代码
    ctx = callback_context
    if not ctx.triggered:
        return []
    
    triggered_id = json.loads(ctx.triggered[0]['prop_id'].rsplit('.', 1)[0])
    symbol = triggered_id['symbol']
    
    try:
        # 执行波动率分析        
        result = analyze_stock_volatility(symbol)
        
        if result.get('success'):
            # 获取图表和摘要
            charts = get_volatility_charts(symbol)
            
            if charts:
                # 构建结果展示
                chart_tabs = []
                for indicator, chart_data in charts.items():
                    chart_tabs.append(
                        dbc.Tab(
                            html.Img(
                                src=chart_data,
                                style={"width": "100%", "cursor": "pointer"},
                                id={"type": "volatility-chart-image", "indicator": indicator, "symbol": symbol}
                            ),
                            label=indicator,
                            tab_id=indicator
                        )
                    )
                
                return [
                    dbc.Alert("分析完成！", color="success", className="mb-2"),
                    dbc.Card([
                        dbc.CardHeader(html.H6("波动率分析图表")),
                        dbc.CardBody([
                            dbc.Tabs(chart_tabs, id=f"volatility-chart-tabs-{symbol}", 
                                   active_tab=list(charts.keys())[0] if charts else None)
                        ])
                    ])
                ]
            else:
                return [dbc.Alert("分析完成，但未生成图表", color="warning")]
        else:
            error_msg = result.get('error', '未知错误')
            return [dbc.Alert(f"分析失败: {error_msg}", color="danger")]
            
    except Exception as e:
        return [dbc.Alert(f"分析出错: {str(e)}", color="danger")]

# 全局波动率刷新按钮回调函数
@app.callback(
    [
        Output("refresh-progress-store", "data", allow_duplicate=True),
        Output("refresh-progress-modal", "is_open", allow_duplicate=True),
        Output("refresh-progress-interval", "disabled", allow_duplicate=True),
        Output("task-queue-data-store", "data", allow_duplicate=True)  # 添加任务队列更新
    ],
    [Input("refresh-volatility-global-btn", "n_clicks")],
    prevent_initial_call=True
)
def handle_global_volatility_refresh_btn(n_clicks):
    """处理全局波动率刷新按钮点击 - 使用多进程刷新所有股票的波动率数据"""
    if not n_clicks:
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update

    # 增强的防重复点击机制：使用时间戳和状态双重检查
    current_time = time.time()
    global volatility_refresh_status, _last_click_timestamps

    # 检查距离上次点击是否少于2秒
    if current_time - _last_click_timestamps['global_volatility_refresh'] < 2.0:
        logger.info("全局波动率刷新按钮点击过于频繁，忽略重复点击")
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update

    # 检查是否已有波动率刷新任务在运行
    if volatility_refresh_status and volatility_refresh_status.get("progress", 100) < 100:
        logger.info("全局波动率刷新任务已在运行中，忽略重复点击")
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update

    # 更新最后点击时间戳
    _last_click_timestamps['global_volatility_refresh'] = current_time

    try:
        # 使用任务优化器处理IO密集型的数据获取和CPU密集型的分析任务
        optimizer = get_task_optimizer()

        def volatility_refresh_task():
            # 导入异步任务管理器
            from quantify.dashview.async_task_manager import submit_batch_volatility_analysis, TaskPriority

            # 获取所有股票代码
            stocks_data = db_ops.get_all_stocks()
            stock_symbols = [data.get('stock_symbol', '') for data in stocks_data.values() if data.get('stock_symbol')]

            if not stock_symbols:
                logger.warning("没有找到需要分析的股票")
                return None, stock_symbols

            # 提交批量波动率分析任务
            task_id = submit_batch_volatility_analysis(stock_symbols, TaskPriority.HIGH)
            logger.info(f"已提交批量波动率分析任务，任务ID: {task_id}，股票数量: {len(stock_symbols)}")

            return task_id, stock_symbols

        # 异步执行波动率刷新任务
        future = optimizer.optimize_task("volatility_analysis", volatility_refresh_task)
        task_id, stock_symbols = future.result()

        if task_id is None:
            return {
                "progress": 100,
                "message": "没有找到需要分析的股票",
                "type": "stock",
                "source": "volatility_refresh"
            }, True, False, dash.no_update

        # 初始化进度信息
        volatility_refresh_status = {
            "progress": 5,
            "message": f"已启动多进程波动率分析，共{len(stock_symbols)}只股票",
            "type": "stock",
            "source": "volatility_refresh",
            "task_id": task_id
        }

        # 获取更新后的任务队列数据
        try:
            from quantify.dashview.async_task_manager import get_task_manager
            multiprocess_manager = get_task_manager()
            updated_queue_data = {
                "current": [],
                "completed": []
            }

            # 获取多进程任务
            mp_tasks = multiprocess_manager.get_all_tasks()
            for task_id, task_info in mp_tasks.items():
                mp_task = {
                    "task_id": task_id,
                    "task_name": f"[多进程] {task_info.task_name}",
                    "task_type": task_info.task_type,
                    "status": task_info.status.value,
                    "created_time": task_info.created_time,
                    "progress": task_info.progress,
                    "details": f"进度: {task_info.progress:.1f}%"
                }
                updated_queue_data["current"].append(mp_task)
        except Exception as queue_error:
            logger.error(f"获取任务队列数据失败: {queue_error}")
            updated_queue_data = dash.no_update

        # 同时更新refresh-progress-store以便监控回调能够处理
        return volatility_refresh_status, True, False, updated_queue_data

    except Exception as e:
        logger.error(f"启动多进程波动率分析失败: {str(e)}")
        return {
            "progress": 100,
            "message": f"启动分析失败: {str(e)}",
            "type": "stock",
            "source": "volatility_refresh"
        }, True, False, dash.no_update

# 波动率刷新按钮回调函数（保留用于兼容性）
@app.callback(
    [
        Output("refresh-progress-store", "data", allow_duplicate=True),
        Output("refresh-progress-modal", "is_open", allow_duplicate=True),
        Output("refresh-progress-interval", "disabled", allow_duplicate=True)
    ],
    [Input({"type": "refresh-volatility-btn", "symbol": ALL}, "n_clicks")],
    prevent_initial_call=True
)
def handle_volatility_refresh_btn(n_clicks_list):
    """处理波动率刷新按钮点击 - 刷新所有股票的波动率数据"""
    # 检查是否有任何按钮被点击
    if not n_clicks_list or not any(click for click in n_clicks_list if click):
        return dash.no_update, dash.no_update, dash.no_update
    
    # 初始化全局进度信息
    global volatility_refresh_status
    volatility_refresh_status = {
        "progress": 20, 
        "message": "波动率分析刷新中...", 
        "type": "stock", 
        "source": "volatility_refresh"
    }
    logger.info("波动率刷新按钮被点击，开始执行全局波动率分析刷新")
    
    # 使用线程在后台执行刷新
    def background_volatility_refresh():
        global volatility_refresh_status
        try:
            # 调用数据库操作类的波动率分析更新方法
            logger.info("开始调用run_volatility_analysis_update...")
            result = db_ops.run_volatility_analysis_update()
            
            if result:
                success_count = result.get("success_count", 0)
                error_count = result.get("error_count", 0)
                updated_data = result.get("updated_data")
                
                # 更新完成，设置进度为100%
                volatility_refresh_status = {
                    "progress": 100,
                    "message": f"波动率分析刷新完成 (成功: {success_count}, 失败: {error_count})",
                    "type": "stock",
                    "source": "volatility_refresh",
                    "updated_data": updated_data
                }
                
                logger.info(f"波动率分析刷新完成 - 成功: {success_count}, 失败: {error_count}")
            else:
                # 没有返回结果
                volatility_refresh_status = {
                    "progress": 100,
                    "message": "波动率分析刷新完成（无结果返回）",
                    "type": "stock",
                    "source": "volatility_refresh"
                }
                logger.warning("波动率分析刷新完成，但未返回结果")
                
        except Exception as e:
            # 发生错误时，显示错误信息
            volatility_refresh_status = {
                "progress": 100,
                "message": f"波动率分析刷新失败: {str(e)}",
                "type": "stock",
                "source": "volatility_refresh"
            }
            logger.error(f"波动率分析刷新出错: {str(e)}")
            import traceback
            logger.error(f"{traceback.format_exc()}")
    
    # 启动后台线程
    thread = Thread(target=background_volatility_refresh)
    thread.daemon = True
    thread.start()
    
    # 立即返回，显示进度模态框
    return volatility_refresh_status, True, False

# 波动率分析相关回调函数
@app.callback(
    [
        Output("strategy-center-main-content", "children"),
        Output("strategy-signals-store", "data")
    ],
    [
        Input("stocks-data-store", "data"),
        Input("strategy-center-refresh-interval", "n_intervals"),  # 策略中心专用刷新
        Input("strategy-center-refresh-trigger", "data")  # 手动刷新触发器
    ],
    prevent_initial_call=False
)
def update_strategy_center_content(stocks_data, center_intervals, refresh_trigger):
    """更新策略中心内容"""
    try:
        # 记录触发原因
        ctx = callback_context
        if ctx.triggered:
            trigger_prop = ctx.triggered[0]['prop_id']
            logger.info(f"策略中心内容更新被触发，触发器: {trigger_prop}")
        
        # 检查是否有波动率刷新完成
        global volatility_refresh_status
        if volatility_refresh_status and volatility_refresh_status.get("progress") == 100:
            logger.info("检测到波动率刷新完成，将获取最新告警数据")
        
        # 获取所有股票代码
        stock_symbols = [data.get('stock_symbol', '') for data in stocks_data.values()]
        
        # 创建股票代码到股票名称的映射
        symbol_to_name = {}
        for data in stocks_data.values():
            symbol = data.get('stock_symbol', '')
            name = data.get('stock_name', '')
            if symbol:
                symbol_to_name[symbol] = name
        
        # 获取波动率告警数据（这会读取最新的文件数据）
        volatility_alerts = get_volatility_alerts(stock_symbols)
        
        # 获取相对分析告警数据
        relative_alerts = analyze_relative_analysis_alerts(stocks_data)
        
        # 转换为新渲染器需要的格式
        from datetime import datetime
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        formatted_volatility_signals = []
        for alert in volatility_alerts:
            symbol = alert.get('symbol', '')
            # 如果告警数据没有更新时间，使用当前时间
            last_updated = alert.get('last_updated', '')
            if not last_updated:
                last_updated = current_time
            
            formatted_signal = {
                'symbol': symbol,
                'name': symbol_to_name.get(symbol, symbol),  # 添加股票名称
                'alert_count': alert.get('alert_count', 0),
                'severity': alert.get('severity', 'low'),
                'last_updated': last_updated,
                'recommendation': alert.get('recommendation', ''),
                'raw_data': alert
            }
            formatted_volatility_signals.append(formatted_signal)
        
        # 获取期权交易信号数据（强制从数据库获取最新数据）
        option_trade_logs = db_ops.get_option_trade_logs()
        formatted_option_signals = []
        
        # 记录期权数据获取状态
        if option_trade_logs:
            logger.info(f"成功获取期权交易信号数据，更新时间: {option_trade_logs.get('updated_at')}")
        else:
            logger.warning("未获取到期权交易信号数据")
        
        if option_trade_logs:
            # 处理期权交易日志数据
            trade_logs = option_trade_logs.get('trade_logs', {})
            signal_logs = option_trade_logs.get('signal_logs', {})
            last_updated = option_trade_logs.get('updated_at', current_time)
            
            # 为每个指数创建信号
            for symbol in ['SH000300', 'SH000852']:
                if symbol in trade_logs or symbol in signal_logs:
                    # 统计日志行数作为信号强度
                    trade_count = 0
                    signal_count = 0
                    
                    if symbol in trade_logs:
                        trade_manager_logs = trade_logs[symbol].get('trade_manager', [])
                        short_trade_manager_logs = trade_logs[symbol].get('short_trade_manager', [])
                        trade_count = len(trade_manager_logs) + len(short_trade_manager_logs)
                    
                    if symbol in signal_logs:
                        signal_short_logs = signal_logs[symbol].get('signal_short', [])
                        signal_count = len(signal_short_logs)
                    
                    # 确定信号强度
                    total_alerts = trade_count + signal_count
                    if total_alerts >= 15:
                        severity = 'high'
                    elif total_alerts >= 10:
                        severity = 'medium'
                    else:
                        severity = 'low'
                    
                    formatted_option_signal = {
                        'symbol': symbol,
                        'name': '沪深300期权' if symbol == 'SH000300' else '中证1000期权',
                        'alert_count': total_alerts,
                        'severity': severity,
                        'last_updated': last_updated,
                        'recommendation': '查看详细交易日志',
                        'raw_data': {
                            'trade_logs': trade_logs,  # 传递完整的trade_logs数据
                            'signal_logs': signal_logs,  # 传递完整的signal_logs数据
                            'updated_at': last_updated,
                            'trade_count': trade_count,
                            'signal_count': signal_count
                        }
                    }
                    formatted_option_signals.append(formatted_option_signal)
        
        # 格式化相对分析告警为股票交易信号
        formatted_stock_signals = []
        for alert in relative_alerts:
            formatted_signal = {
                'symbol': alert['symbol'],
                'name': alert['name'],
                'alert_count': alert['alert_count'],
                'severity': alert['severity'],
                'last_updated': alert.get('last_updated', current_time),
                'recommendation': f"相对分析告警: {alert['alert_count']}个异常",
                'raw_data': alert
            }
            formatted_stock_signals.append(formatted_signal)
        
        # 将相对分析告警数据传递给策略渲染器
        strategy_renderer._relative_alerts_cache = relative_alerts
        
        # 准备信号数据
        signals_data = strategy_renderer.format_signals_data(
            volatility_alerts=formatted_volatility_signals,
            stock_signals=formatted_stock_signals,  # 相对分析告警
            option_signals=formatted_option_signals  # 期权交易信号
        )
        
        # 使用新渲染器渲染策略中心
        strategy_center_components = strategy_renderer.render_strategy_center(signals_data)
        
        return strategy_center_components, signals_data
        
    except Exception as e:
        logger.error(f"更新策略中心内容失败: {e}")
        return [html.P("加载策略中心内容失败", className="text-danger")], {}

# 手动触发期权交易信号更新的回调函数
@app.callback(
    [
        Output("refresh-progress-store", "data", allow_duplicate=True),
        Output("refresh-progress-modal", "is_open", allow_duplicate=True),
        Output("refresh-progress-interval", "disabled", allow_duplicate=True)
    ],
    [Input("refresh-option_trade-alerts", "n_clicks")],
    prevent_initial_call=True
)
def handle_option_trade_signal_refresh(n_clicks):
    """处理期权交易信号手动刷新按钮点击"""
    if not n_clicks:
        return dash.no_update, dash.no_update, dash.no_update

    # 防止重复点击：检查是否已有期权交易信号更新任务在运行
    global option_trade_refresh_status
    if (option_trade_refresh_status and
        1 <= option_trade_refresh_status.get("progress", 100) < 100 and
        option_trade_refresh_status.get("source") == "option_trade_signal_refresh"):
        logger.info("期权交易信号更新任务已在运行中，忽略重复点击")
        return dash.no_update, dash.no_update, dash.no_update

    try:
        # 使用多进程任务管理器提交任务
        from quantify.dashview.async_task_manager import submit_option_trade_signal_update, TaskPriority

        logger.info("期权交易信号手动刷新按钮被点击，提交多进程任务...")

        # 提交期权交易信号更新任务
        task_id = submit_option_trade_signal_update(TaskPriority.HIGH)

        # 初始化全局进度信息
        option_trade_refresh_status = {
            "progress": 5,
            "message": "期权交易信号更新任务已提交...",
            "type": "option_trade",
            "source": "option_trade_signal_refresh",
            "task_id": task_id
        }

        logger.info(f"期权交易信号更新任务已提交，任务ID: {task_id}")

        # 立即返回，显示进度弹窗
        return option_trade_refresh_status, True, False

    except Exception as e:
        logger.error(f"提交期权交易信号更新任务失败: {str(e)}")

        # 返回错误状态
        error_status = {
            "progress": 100,
            "message": f"期权交易信号更新任务提交失败: {str(e)}",
            "type": "option_trade",
            "source": "option_trade_signal_refresh"
        }

        return error_status, True, False

# 添加波动率告警刷新回调 - 使用固定ID但通过suppress_callback_exceptions处理
@app.callback(
    [
        Output("refresh-progress-store", "data", allow_duplicate=True),
        Output("refresh-progress-modal", "is_open", allow_duplicate=True),
        Output("refresh-progress-interval", "disabled", allow_duplicate=True),
        Output("strategy-center-refresh-interval", "disabled", allow_duplicate=True)  # 暂停自动刷新
    ],
    [Input("refresh-volatility-alerts", "n_clicks")],
    prevent_initial_call=True
)
def handle_volatility_alerts_refresh(n_clicks):
    """处理波动率告警刷新按钮点击 - 使用多进程任务管理器刷新波动率数据"""
    if not n_clicks:
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update

    # 增强的防重复点击机制：使用时间戳和状态双重检查
    current_time = time.time()
    global volatility_refresh_status, _last_click_timestamps

    # 检查距离上次点击是否少于2秒
    if current_time - _last_click_timestamps['volatility_alerts_refresh'] < 2.0:
        logger.info("波动率告警刷新按钮点击过于频繁，忽略重复点击")
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update

    # 检查是否已有波动率刷新任务在运行
    if (volatility_refresh_status and
        1 <= volatility_refresh_status.get("progress", 100) < 100 and
        volatility_refresh_status.get("source") == "volatility_alerts_refresh"):
        logger.info("波动率告警刷新任务已在运行中，忽略重复点击")
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update

    # 更新最后点击时间戳
    _last_click_timestamps['volatility_alerts_refresh'] = current_time
    logger.info("手动刷新波动率告警数据")

    try:
        # 使用任务优化器处理IO密集型的数据库查询
        optimizer = get_task_optimizer()

        def volatility_alerts_refresh_task():
            """波动率告警刷新任务"""
            # 获取所有股票数据
            stocks_data = db_ops.get_all_stocks()
            stock_symbols = [data.get('stock_symbol', '') for data in stocks_data.values()]

            if not stock_symbols:
                logger.warning("没有找到需要分析的股票")
                return None, []

            # 使用多进程任务管理器提交批量波动率分析任务
            from quantify.dashview.async_task_manager import submit_batch_volatility_analysis, TaskPriority
            task_id = submit_batch_volatility_analysis(stock_symbols, TaskPriority.HIGH)

            logger.info(f"已提交批量波动率分析任务，任务ID: {task_id}, 股票数量: {len(stock_symbols)}")
            return task_id, stock_symbols

        # 异步执行波动率告警刷新任务
        future = optimizer.optimize_task("volatility_analysis", volatility_alerts_refresh_task)
        task_id, stock_symbols = future.result()

        if task_id is None:
            return {
                "progress": 100,
                "message": "没有找到需要分析的股票",
                "type": "stock",
                "source": "volatility_alerts_refresh"
            }, True, False, True

        # 初始化进度信息
        volatility_refresh_status = {
            "progress": 5,
            "message": f"已启动多进程波动率分析，共{len(stock_symbols)}只股票",
            "type": "stock",
            "source": "volatility_alerts_refresh",
            "task_id": task_id
        }

        # 立即返回，显示进度弹窗，暂停自动刷新
        return volatility_refresh_status, True, False, True

    except Exception as e:
        logger.error(f"启动多进程波动率分析失败: {str(e)}")
        return {
            "progress": 100,
            "message": f"启动分析失败: {str(e)}",
            "type": "stock",
            "source": "volatility_alerts_refresh"
        }, True, False, True

# 添加股票交易信号刷新回调 - 使用固定ID但通过suppress_callback_exceptions处理
@app.callback(
    [
        Output("refresh-progress-store", "data", allow_duplicate=True),
        Output("refresh-progress-modal", "is_open", allow_duplicate=True),
        Output("refresh-progress-interval", "disabled", allow_duplicate=True),
        Output("strategy-center-refresh-interval", "disabled", allow_duplicate=True)  # 暂停自动刷新
    ],
    [Input("refresh-stock_trade-alerts", "n_clicks")],
    prevent_initial_call=True
)
def handle_stock_trade_alerts_refresh(n_clicks):
    """处理股票交易信号刷新按钮点击 - 清除缓存并重新获取股票交易信号数据"""
    if not n_clicks:
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update

    # 防止重复点击：检查是否已有股票交易信号刷新任务在运行
    global stock_refresh_status
    if stock_refresh_status and stock_refresh_status.get("progress", 100) < 100 and stock_refresh_status.get("source") == "stock_trade_refresh":
        logger.info("股票交易信号刷新任务已在运行中，忽略重复点击")
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update

    logger.info("手动刷新股票交易信号数据")

    # 设置全局刷新状态
    stock_refresh_status = {"progress": 0, "message": "开始刷新股票交易信号数据...", "processed": False, "type": "stock", "source": "stock_trade_refresh"}

    def background_stock_trade_refresh():
        """后台刷新股票交易信号数据"""
        try:
            # 更新进度
            stock_refresh_status["progress"] = 10
            stock_refresh_status["message"] = "清除股票策略缓存..."

            # 清除股票策略相关的缓存
            # 清除前端数据存储
            # 这里可以根据实际的缓存机制来清除相关缓存
            logger.info("开始清除股票策略缓存")

            stock_refresh_status["progress"] = 30
            stock_refresh_status["message"] = "重新获取股票策略数据..."

            stock_refresh_status["progress"] = 50
            stock_refresh_status["message"] = "正在更新股票策略分析..."

            # 执行股票策略刷新
            try:
                # 应该不需要，因为这里的刷新按钮只是为了从数据库中刷新数据
                # refresh_data() 
                logger.info("股票策略数据刷新成功")

                stock_refresh_status["progress"] = 80
                stock_refresh_status["message"] = "更新数据库中的股票策略数据..."

                # 使用任务优化器获取更新后的数据（用于触发界面刷新）
                optimizer = get_task_optimizer()

                def get_updated_stocks_task():
                    return db_ops.get_all_stocks()

                # 异步获取更新后的股票数据
                future = optimizer.optimize_task("database_operations", get_updated_stocks_task)
                global stocks_data
                stocks_data = future.result()

                stock_refresh_status["progress"] = 100
                stock_refresh_status["message"] = "股票交易信号数据刷新完成！"

                logger.info("股票交易信号数据刷新完成")

            except Exception as e:
                logger.error(f"股票策略刷新失败: {e}")
                stock_refresh_status["progress"] = 100
                stock_refresh_status["message"] = f"刷新失败: {str(e)}"

        except Exception as e:
            logger.error(f"股票交易信号数据刷新失败: {e}")
            stock_refresh_status["progress"] = 100
            stock_refresh_status["message"] = f"刷新失败: {str(e)}"

    # 启动后台线程
    thread = Thread(target=background_stock_trade_refresh)
    thread.daemon = True
    thread.start()

    # 立即返回，显示进度弹窗，暂停自动刷新
    return stock_refresh_status, True, False, True

# 添加策略中心刷新触发器回调
@app.callback(
    Output("strategy-center-refresh-trigger", "data"),
    [
        Input("refresh-option_trade-alerts", "n_clicks"),
        Input("refresh-volatility-alerts", "n_clicks"),  # 波动率告警刷新触发器
        Input("refresh-stock_trade-alerts", "n_clicks"),  # 股票交易信号刷新触发器
        Input("refresh-volatility-global-btn", "n_clicks"),
        Input({"type": "refresh-volatility-btn", "symbol": ALL}, "n_clicks"),
    ],
    [State("strategy-center-refresh-trigger", "data")],
    prevent_initial_call=True
)
def trigger_strategy_center_refresh(*_):
    """当有任何刷新操作时，触发策略中心刷新"""
    ctx = callback_context
    if not ctx.triggered:
        return dash.no_update

    # 使用当前时间戳作为触发器
    return {"timestamp": time.time()}

# 添加后台完成状态监听回调
@app.callback(
    [
        Output("strategy-center-refresh-trigger", "data", allow_duplicate=True),
        Output("strategy-center-refresh-interval", "disabled", allow_duplicate=True)  # 重新启用自动刷新
    ],
    [Input("refresh-progress-interval", "n_intervals")],
    [
        State("refresh-progress-store", "data"),
        State("strategy-center-refresh-trigger", "data")
    ],
    prevent_initial_call=True
)
def monitor_background_completion(*_):
    """监听后台刷新完成状态，自动触发策略中心刷新"""
    global stock_refresh_status, volatility_refresh_status

    # 检查各种刷新状态是否完成
    refresh_completed = False

    # 检查普通刷新
    if stock_refresh_status and stock_refresh_status.get("progress") == 100 and stock_refresh_status.get("source") in ["refresh", "option_trade_refresh"]:
        # 检查是否已经处理过这次刷新
        if not stock_refresh_status.get("processed", False):
            refresh_completed = True
            stock_refresh_status["processed"] = True  # 标记为已处理
            logger.info("检测到数据刷新完成，触发策略中心刷新")

    # 检查波动率刷新
    if volatility_refresh_status and volatility_refresh_status.get("progress") == 100:
        # 检查是否已经处理过这次刷新
        if not volatility_refresh_status.get("processed", False):
            refresh_completed = True
            volatility_refresh_status["processed"] = True  # 标记为已处理
            logger.info("检测到波动率刷新完成，触发策略中心刷新")

    if refresh_completed:
        # 刷新完成后，触发策略中心刷新并重新启用自动刷新
        return {"timestamp": time.time()}, False

    return dash.no_update, dash.no_update


@app.callback(
    [
        Output("image-modal", "is_open", allow_duplicate=True),
        Output("enlarged-image", "src", allow_duplicate=True),
        Output("image-modal-title", "children", allow_duplicate=True)
    ],
    [Input({"type": "volatility-chart-image", "indicator": ALL, "symbol": ALL}, "n_clicks")],
    [State("image-modal", "is_open")],
    prevent_initial_call=True
)
def open_volatility_chart_modal(chart_clicks, is_open):
    """打开波动率图表放大模态框"""
    ctx = callback_context
    if not ctx.triggered or not any(click for click in chart_clicks if click):
        return dash.no_update, dash.no_update, dash.no_update
    
    # 找到被点击的图表
    for trigger in ctx.triggered:
        if "volatility-chart-image" in trigger['prop_id']:
            trigger_id = json.loads(trigger['prop_id'].rsplit('.', 1)[0])
            indicator = trigger_id['indicator']
            symbol = trigger_id['symbol']
            
            # 获取图表数据
            charts = get_volatility_charts(symbol)
            if indicator in charts:
                return True, charts[indicator], f"{symbol} - {indicator} 波动率分析"
    
    return dash.no_update, dash.no_update, dash.no_update

# 添加策略中心告警展开/收起回调函数
@app.callback(
    [
        Output("volatility-alerts-collapse", "is_open"),
        Output("expand-volatility-alerts", "children"),
        Output("expand-volatility-alerts", "style"),
        Output("volatility-alerts-card-body", "style"),
        Output("strategy-center-expand-state", "data", allow_duplicate=True)
    ],
    [
        Input("expand-volatility-alerts", "n_clicks"),
        Input("strategy-signals-store", "data")  # 监听数据更新
    ],
    [
        State("volatility-alerts-collapse", "is_open"),
        State("strategy-center-expand-state", "data")
    ],
    prevent_initial_call=True
)
def toggle_volatility_alerts_collapse(n_clicks, signals_data, is_open, expand_state):
    """切换波动率告警展开/收起状态"""
    ctx = callback_context
    if not ctx.triggered:
        return is_open, dash.no_update, dash.no_update, dash.no_update, dash.no_update

    triggered_id = ctx.triggered[0]['prop_id'].split('.')[0]
    volatility_alerts = signals_data.get('volatility', [])
    hidden_count = max(0, len(volatility_alerts) - 3)

    # 初始化展开状态
    if expand_state is None:
        expand_state = {}

    # 如果是数据更新触发，恢复之前的展开状态
    if triggered_id == "strategy-signals-store":
        saved_state = expand_state.get('volatility-alerts', False)
        is_open = saved_state
        new_is_open = saved_state
    # 如果是按钮点击
    elif triggered_id == "expand-volatility-alerts" and n_clicks:
        new_is_open = not is_open
        # 保存新的展开状态
        expand_state['volatility-alerts'] = new_is_open
    else:
        new_is_open = is_open
        
        if new_is_open:
            # 展开状态
            button_content = [
                html.I(className="fas fa-chevron-up me-1"),
                "收起"
            ]
            # 展开时移除高度限制
            card_body_style = {"maxHeight": "none", "overflowY": "visible"}
        else:
            # 收起状态
            button_content = [
                html.I(className="fas fa-chevron-down me-1"),
                f"展开 ({hidden_count})" if hidden_count > 0 else "展开"
            ]
            # 收起时恢复高度限制
            card_body_style = {
                "maxHeight": "300px" if hidden_count > 0 else "auto", 
                "overflowY": "auto" if hidden_count > 0 else "visible"
            }
        
        return new_is_open, button_content, {"display": "block"}, card_body_style, expand_state

    # 处理按钮显示逻辑
    if new_is_open:
        # 当前是展开状态
        button_content = [
            html.I(className="fas fa-chevron-up me-1"),
            "收起"
        ]
        card_body_style = {"maxHeight": "none", "overflowY": "visible"}
    else:
        # 当前是收起状态
        button_content = [
            html.I(className="fas fa-chevron-down me-1"),
            f"展开 ({hidden_count})" if hidden_count > 0 else "展开"
        ]
        card_body_style = {
            "maxHeight": "300px" if hidden_count > 0 else "auto",
            "overflowY": "auto" if hidden_count > 0 else "visible"
        }

    button_style = {"display": "block" if hidden_count > 0 else "none"}
    return new_is_open, button_content, button_style, card_body_style, expand_state

@app.callback(
    [
        Output("stock_trade-alerts-collapse", "is_open"),
        Output("expand-stock_trade-alerts", "children"),
        Output("expand-stock_trade-alerts", "style"),
        Output("stock_trade-alerts-card-body", "style"),
        Output("strategy-center-expand-state", "data", allow_duplicate=True)
    ],
    [
        Input("expand-stock_trade-alerts", "n_clicks"),
        Input("strategy-signals-store", "data")  # 监听数据更新
    ],
    [
        State("stock_trade-alerts-collapse", "is_open"),
        State("strategy-center-expand-state", "data")
    ],
    prevent_initial_call=True
)
def toggle_stock_trade_alerts_collapse(n_clicks, signals_data, is_open, expand_state):
    """切换股票交易信号展开/收起状态"""
    ctx = callback_context
    if not ctx.triggered:
        return is_open, dash.no_update, dash.no_update, dash.no_update, dash.no_update

    triggered_id = ctx.triggered[0]['prop_id'].split('.')[0]
    stock_alerts = signals_data.get('stock_trade', [])
    hidden_count = max(0, len(stock_alerts) - 3)

    # 初始化展开状态
    if expand_state is None:
        expand_state = {}

    # 如果是数据更新触发，恢复之前的展开状态
    if triggered_id == "strategy-signals-store":
        saved_state = expand_state.get('stock_trade-alerts', False)
        is_open = saved_state
        new_is_open = saved_state
    # 如果是按钮点击
    elif triggered_id == "expand-stock_trade-alerts" and n_clicks:
        new_is_open = not is_open
        # 保存新的展开状态
        expand_state['stock_trade-alerts'] = new_is_open
    else:
        new_is_open = is_open
        
        if new_is_open:
            # 展开状态
            button_content = [
                html.I(className="fas fa-chevron-up me-1"),
                "收起"
            ]
            # 展开时移除高度限制
            card_body_style = {"maxHeight": "none", "overflowY": "visible"}
        else:
            # 收起状态
            button_content = [
                html.I(className="fas fa-chevron-down me-1"),
                f"展开 ({hidden_count})" if hidden_count > 0 else "展开"
            ]
            # 收起时恢复高度限制
            card_body_style = {
                "maxHeight": "300px" if hidden_count > 0 else "auto", 
                "overflowY": "auto" if hidden_count > 0 else "visible"
            }
        
        return new_is_open, button_content, {"display": "block"}, card_body_style, expand_state

    # 处理按钮显示逻辑
    if new_is_open:
        # 当前是展开状态
        button_content = [
            html.I(className="fas fa-chevron-up me-1"),
            "收起"
        ]
        card_body_style = {"maxHeight": "none", "overflowY": "visible"}
    else:
        # 当前是收起状态
        button_content = [
            html.I(className="fas fa-chevron-down me-1"),
            f"展开 ({hidden_count})" if hidden_count > 0 else "展开"
        ]
        card_body_style = {
            "maxHeight": "300px" if hidden_count > 0 else "auto",
            "overflowY": "auto" if hidden_count > 0 else "visible"
        }

    button_style = {"display": "block" if hidden_count > 0 else "none"}
    return new_is_open, button_content, button_style, card_body_style, expand_state

@app.callback(
    [
        Output("option_trade-alerts-collapse", "is_open"),
        Output("expand-option_trade-alerts", "children"),
        Output("expand-option_trade-alerts", "style"),
        Output("option_trade-alerts-card-body", "style"),
        Output("strategy-center-expand-state", "data", allow_duplicate=True)
    ],
    [
        Input("expand-option_trade-alerts", "n_clicks"),
        Input("strategy-signals-store", "data")  # 监听数据更新
    ],
    [
        State("option_trade-alerts-collapse", "is_open"),
        State("strategy-center-expand-state", "data")
    ],
    prevent_initial_call=True
)
def toggle_option_trade_alerts_collapse(n_clicks, signals_data, is_open, expand_state):
    """切换期权交易信号展开/收起状态"""
    ctx = callback_context
    if not ctx.triggered:
        return is_open, dash.no_update, dash.no_update, dash.no_update, dash.no_update

    triggered_id = ctx.triggered[0]['prop_id'].split('.')[0]
    option_alerts = signals_data.get('option_trade', [])
    hidden_count = max(0, len(option_alerts) - 3)

    # 初始化展开状态
    if expand_state is None:
        expand_state = {}

    # 如果是数据更新触发，恢复之前的展开状态
    if triggered_id == "strategy-signals-store":
        saved_state = expand_state.get('option_trade-alerts', False)
        is_open = saved_state
        new_is_open = saved_state
    # 如果是按钮点击
    elif triggered_id == "expand-option_trade-alerts" and n_clicks:
        new_is_open = not is_open
        # 保存新的展开状态
        expand_state['option_trade-alerts'] = new_is_open
    else:
        new_is_open = is_open
        
        if new_is_open:
            # 展开状态
            button_content = [
                html.I(className="fas fa-chevron-up me-1"),
                "收起"
            ]
            # 展开时移除高度限制
            card_body_style = {"maxHeight": "none", "overflowY": "visible"}
        else:
            # 收起状态
            button_content = [
                html.I(className="fas fa-chevron-down me-1"),
                f"展开 ({hidden_count})" if hidden_count > 0 else "展开"
            ]
            # 收起时恢复高度限制
            card_body_style = {
                "maxHeight": "300px" if hidden_count > 0 else "auto", 
                "overflowY": "auto" if hidden_count > 0 else "visible"
            }
        
        return new_is_open, button_content, {"display": "block"}, card_body_style, expand_state

    # 处理按钮显示逻辑
    if new_is_open:
        # 当前是展开状态
        button_content = [
            html.I(className="fas fa-chevron-up me-1"),
            "收起"
        ]
        card_body_style = {"maxHeight": "none", "overflowY": "visible"}
    else:
        # 当前是收起状态
        button_content = [
            html.I(className="fas fa-chevron-down me-1"),
            f"展开 ({hidden_count})" if hidden_count > 0 else "展开"
        ]
        card_body_style = {
            "maxHeight": "300px" if hidden_count > 0 else "auto",
            "overflowY": "auto" if hidden_count > 0 else "visible"
        }

    button_style = {"display": "block" if hidden_count > 0 else "none"}
    return new_is_open, button_content, button_style, card_body_style, expand_state

@app.callback(
    [
        Output("signal-detail-modal", "is_open"),
        Output("signal-detail-modal-title", "children"),
        Output("signal-detail-modal-body", "children")
    ],
    [
        Input({"type": "view-signal-details", "signal_type": ALL, "symbol": ALL}, "n_clicks"),
        Input("close-signal-detail-modal", "n_clicks")
    ],
    [
        State("signal-detail-modal", "is_open"),
        State("strategy-signals-store", "data")
    ],
    prevent_initial_call=True
)
def toggle_signal_detail_modal(detail_clicks, close_clicks, is_open, signals_data):
    """处理信号详情模态框的显示/隐藏"""
    ctx = dash.callback_context
    if not ctx.triggered:
        return False, "", ""
    
    trigger_id = ctx.triggered[0]['prop_id']
    
    # 如果点击关闭按钮
    if "close-signal-detail-modal" in trigger_id:
        return False, "", ""
    
    # 如果点击查看详情按钮
    if "view-signal-details" in trigger_id and any(detail_clicks):
        # 获取被点击按钮的ID信息
        button_id_str = ctx.triggered[0]['prop_id'].rsplit('.', 1)[0]
        button_id = json.loads(button_id_str)
        
        signal_type = button_id['signal_type']
        symbol = button_id['symbol']
        
        # 根据信号类型获取对应的原始数据
        raw_data = {}
        if signal_type == SignalType.VOLATILITY and signals_data.get(SignalType.VOLATILITY):
            for signal in signals_data[SignalType.VOLATILITY]:
                if signal.get('symbol') == symbol:
                    raw_data = signal.get('raw_data', {})
                    break
        elif signal_type == SignalType.STOCK_TRADE and signals_data.get(SignalType.STOCK_TRADE):
            for signal in signals_data[SignalType.STOCK_TRADE]:
                if signal.get('symbol') == symbol:
                    raw_data = signal.get('raw_data', {})
                    break
        elif signal_type == SignalType.OPTION_TRADE and signals_data.get(SignalType.OPTION_TRADE):
            for signal in signals_data[SignalType.OPTION_TRADE]:
                if signal.get('symbol') == symbol:
                    raw_data = signal.get('raw_data', {})
                    break
        
        # 使用策略中心渲染器生成详情内容
        detail_content = strategy_renderer.render_signal_details(signal_type, symbol, raw_data)
        
        # 信号类型中文名称
        signal_type_names = {
            SignalType.VOLATILITY: "波动率信号",
            SignalType.STOCK_TRADE: "股票交易信号",
            SignalType.OPTION_TRADE: "期权交易信号"
        }
        
        signal_type_name = signal_type_names.get(signal_type, "信号")
        
        return True, f"{symbol} - {signal_type_name}详情", detail_content
    
    return False, "", ""


def get_alert_color(severity):
    """根据严重程度返回颜色"""
    color_map = {
        'high': 'danger',
        'medium': 'warning',
        'low': 'info',
        '高': 'danger',
        '中': 'warning',
        '低': 'info'
    }
    return color_map.get(severity.lower(), 'secondary')

# 注册应用关闭时的清理函数
import atexit

def cleanup_on_exit():
    """应用退出时的清理函数"""
    try:
        stop_scheduler()

        # 关闭任务优化器
        try:
            from quantify.dashview.task_optimizer import shutdown_task_optimizer
            shutdown_task_optimizer()
        except Exception as e:
            # 忽略任务优化器关闭错误
            pass

        # 关闭多进程任务管理器
        try:
            from quantify.dashview.async_task_manager import shutdown_task_manager
            shutdown_task_manager()
        except Exception as e:
            # 忽略任务管理器关闭错误
            pass

        # 避免在测试环境中stdout/stderr已关闭导致的logging报错
        # 不进行日志输出
    except Exception as e:
        # 测试环境下可能已关闭输出流，忽略日志
        pass

# 注册退出处理器
atexit.register(cleanup_on_exit)

# 启动服务器
if __name__ == '__main__':
    try:
        initialize_app()

        # 初始化多进程任务管理器
        try:
            from quantify.dashview.async_task_manager import get_task_manager, register_task_monitor_callbacks

            # 启动任务管理器
            task_manager = get_task_manager()
            logger.info("多进程任务管理器已启动")

            # 注册任务监控回调函数
            try:
                register_task_monitor_callbacks(app)
                logger.info("任务监控回调函数已注册")
            except Exception as e:
                logger.warning(f"注册任务监控回调函数失败: {str(e)}")

        except Exception as e:
            logger.error(f"初始化多进程任务管理器失败: {str(e)}")

        app.run(debug=True, port=8070, threaded=True, use_reloader=False)
        # app.run(debug=True, port=8070, threaded=True)
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在关闭应用...")
        cleanup_on_exit()
    except Exception as e:
        logger.error(f"应用运行失败: {e}")
        cleanup_on_exit()